#!/usr/bin/env python3
"""
PythonAnywhere Web App Setup Script
==================================

This script helps configure the PythonAnywhere web app settings
for the SMSMali Django application.

This script should be run AFTER deploy_to_pythonanywhere.py

Usage:
    python setup_pythonanywhere_webapp.py

Author: SMSMali Development Team
"""

import os
import sys
import json
from pathlib import Path

class PythonAnywhereWebAppSetup:
    def __init__(self):
        self.username = "smsmali"
        self.domain = f"{self.username}.pythonanywhere.com"
        self.project_dir = f"/home/<USER>/smsmali"
        self.static_dir = f"{self.project_dir}/staticfiles"
        self.media_dir = f"{self.project_dir}/media"
        
        print("🔧 PythonAnywhere Web App Configuration")
        print(f"👤 Username: {self.username}")
        print(f"🌐 Domain: {self.domain}")
        print("-" * 50)

    def print_webapp_config(self):
        """Print the web app configuration instructions"""
        print("\n📋 WEB APP CONFIGURATION INSTRUCTIONS")
        print("=" * 50)
        
        print("\n1. 🌐 WEB APP SETTINGS:")
        print(f"   • Go to: https://www.pythonanywhere.com/user/{self.username}/webapps/")
        print(f"   • Domain: {self.domain}")
        print("   • Python version: 3.10 (or latest available)")
        print("   • Framework: Manual configuration")
        
        print("\n2. 📁 SOURCE CODE:")
        print(f"   • Source code directory: {self.project_dir}")
        
        print("\n3. 🐍 WSGI CONFIGURATION:")
        print(f"   • WSGI configuration file: {self.project_dir}/wsgi.py")
        
        print("\n4. 📂 STATIC FILES MAPPING:")
        print("   Add these static file mappings:")
        print(f"   • URL: /static/")
        print(f"   • Directory: {self.static_dir}")
        print(f"   • URL: /media/")
        print(f"   • Directory: {self.media_dir}")
        
        print("\n5. 🔧 VIRTUAL ENVIRONMENT:")
        print(f"   • Virtualenv path: {self.project_dir}/venv")
        
        print("\n6. 🌍 ENVIRONMENT VARIABLES:")
        print("   Add these environment variables in the Web tab:")
        print("   • DJANGO_ENV = production")
        print("   • DJANGO_SECRET_KEY = your-secret-key-here")
        
        print("\n7. 🔄 RELOAD WEB APP:")
        print("   • Click the 'Reload' button to apply changes")

    def create_pa_config_file(self):
        """Create a configuration file for PythonAnywhere"""
        config = {
            "webapp_settings": {
                "domain": self.domain,
                "python_version": "3.10",
                "source_code": self.project_dir,
                "wsgi_file": f"{self.project_dir}/wsgi.py",
                "virtualenv": f"{self.project_dir}/venv"
            },
            "static_files": [
                {
                    "url": "/static/",
                    "directory": self.static_dir
                },
                {
                    "url": "/media/",
                    "directory": self.media_dir
                }
            ],
            "environment_variables": {
                "DJANGO_ENV": "production",
                "DJANGO_SECRET_KEY": "your-secret-key-here"
            }
        }
        
        config_file = Path("pythonanywhere_config.json")
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"\n✅ Configuration saved to: {config_file}")

    def print_deployment_checklist(self):
        """Print deployment checklist"""
        print("\n✅ DEPLOYMENT CHECKLIST")
        print("=" * 30)
        
        checklist = [
            "Run deploy_to_pythonanywhere.py script",
            "Create/configure web app on PythonAnywhere",
            "Set WSGI configuration file",
            "Configure static files mapping",
            "Configure media files mapping", 
            "Set virtual environment path",
            "Add environment variables",
            "Generate and set SECRET_KEY",
            "Test the application",
            "Set up custom domain (if needed)"
        ]
        
        for i, item in enumerate(checklist, 1):
            print(f"   {i:2d}. [ ] {item}")

    def generate_secret_key(self):
        """Generate a Django secret key"""
        try:
            from django.core.management.utils import get_random_secret_key
            secret_key = get_random_secret_key()
            print(f"\n🔑 GENERATED SECRET KEY:")
            print(f"   {secret_key}")
            print("\n⚠️  IMPORTANT: Save this key securely and add it to environment variables!")
            return secret_key
        except ImportError:
            print("\n⚠️  Django not available. Please generate a secret key manually.")
            print("   You can use: python -c 'from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())'")
            return None

    def print_troubleshooting(self):
        """Print troubleshooting tips"""
        print("\n🔧 TROUBLESHOOTING TIPS")
        print("=" * 25)
        
        tips = [
            "Check error logs in PythonAnywhere Web tab",
            "Ensure all file paths are correct",
            "Verify virtual environment is activated",
            "Check static files are collected properly",
            "Ensure database migrations are applied",
            "Verify environment variables are set",
            "Check ALLOWED_HOSTS in Django settings",
            "Ensure CORS settings are configured for production"
        ]
        
        for i, tip in enumerate(tips, 1):
            print(f"   {i}. {tip}")

    def print_useful_commands(self):
        """Print useful commands for PythonAnywhere"""
        print("\n💻 USEFUL COMMANDS")
        print("=" * 20)
        
        commands = [
            ("Check Django version", f"cd {self.project_dir} && python manage.py --version"),
            ("Run migrations", f"cd {self.project_dir} && python manage.py migrate"),
            ("Collect static files", f"cd {self.project_dir} && python manage.py collectstatic"),
            ("Create superuser", f"cd {self.project_dir} && python manage.py createsuperuser"),
            ("Check deployment", f"cd {self.project_dir} && python manage.py check --deploy"),
            ("View error logs", "tail -f /var/log/smsmali.pythonanywhere.com.error.log"),
            ("Restart web app", "touch /var/www/smsmali_pythonanywhere_com_wsgi.py")
        ]
        
        for desc, cmd in commands:
            print(f"\n   📌 {desc}:")
            print(f"      {cmd}")

    def setup(self):
        """Main setup process"""
        self.print_webapp_config()
        self.create_pa_config_file()
        self.generate_secret_key()
        self.print_deployment_checklist()
        self.print_troubleshooting()
        self.print_useful_commands()
        
        print("\n🎉 Setup instructions generated!")
        print(f"📖 Follow the instructions above to configure your web app at:")
        print(f"   https://www.pythonanywhere.com/user/{self.username}/webapps/")

def main():
    setup = PythonAnywhereWebAppSetup()
    setup.setup()

if __name__ == "__main__":
    main()
