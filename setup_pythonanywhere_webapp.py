#!/usr/bin/env python3
"""
Advanced PythonAnywhere Web App Setup Script
===========================================

This script provides advanced configuration for PythonAnywhere web app settings
for the SMSMali Django application with auto-detection and validation.

Features:
- Auto-detect project directory and paths
- Validate Django project structure
- Generate secret keys
- Create configuration files
- Test deployment readiness
- Provide detailed setup instructions

Usage:
    python setup_pythonanywhere_webapp.py [--auto-detect] [--validate] [--generate-config]

Author: SMSMali Development Team
"""

import os
import sys
import json
import subprocess
import secrets
import string
from pathlib import Path
import argparse

class AdvancedPythonAnywhereWebAppSetup:
    def __init__(self, auto_detect=True):
        self.username = "smsmali"
        self.domain = f"{self.username}.pythonanywhere.com"
        self.auto_detect = auto_detect

        # Auto-detect project directory
        if auto_detect:
            self.project_dir = self.detect_project_directory()
        else:
            self.project_dir = f"/home/<USER>/SMSMali"

        self.static_dir = f"{self.project_dir}/staticfiles"
        self.media_dir = f"{self.project_dir}/media"
        self.venv_dir = f"{self.project_dir}/venv"
        self.wsgi_file = f"{self.project_dir}/wsgi.py"

        print("🔧 Advanced PythonAnywhere Web App Configuration")
        print(f"👤 Username: {self.username}")
        print(f"🌐 Domain: {self.domain}")
        print(f"📁 Project Directory: {self.project_dir}")
        print("-" * 60)

    def detect_project_directory(self):
        """Auto-detect the project directory"""
        possible_paths = [
            f"/home/<USER>/SMSMali",
            f"/home/<USER>/smsmali",
            f"/home/<USER>/SMSMali-main",
            Path.cwd(),  # Current directory
        ]

        for path in possible_paths:
            path_obj = Path(path)
            if path_obj.exists() and (path_obj / "manage.py").exists():
                print(f"✅ Auto-detected project directory: {path}")
                return str(path_obj)

        print("⚠️ Could not auto-detect project directory, using default")
        return f"/home/<USER>/SMSMali"

    def validate_project_structure(self):
        """Validate Django project structure"""
        print("\n🔍 Validating Project Structure...")

        required_files = {
            "manage.py": "Django management script",
            "smsmali/settings.py": "Django settings",
            "smsmali/__init__.py": "Django package init",
            "requirements.txt": "Python dependencies"
        }

        optional_files = {
            "wsgi.py": "WSGI configuration",
            "smsmali/settings_production.py": "Production settings",
            "static/frontend/index.html": "Frontend build",
            "venv/bin/activate": "Virtual environment (Linux)",
            "venv/Scripts/activate": "Virtual environment (Windows)"
        }

        project_path = Path(self.project_dir)
        validation_results = {"required": {}, "optional": {}}

        # Check required files
        for file_path, description in required_files.items():
            full_path = project_path / file_path
            exists = full_path.exists()
            validation_results["required"][file_path] = exists
            status = "✅" if exists else "❌"
            print(f"   {status} {file_path} - {description}")

        # Check optional files
        print("\n📋 Optional Files:")
        for file_path, description in optional_files.items():
            full_path = project_path / file_path
            exists = full_path.exists()
            validation_results["optional"][file_path] = exists
            status = "✅" if exists else "⚠️"
            print(f"   {status} {file_path} - {description}")

        # Overall validation
        required_missing = [f for f, exists in validation_results["required"].items() if not exists]
        if required_missing:
            print(f"\n❌ Missing required files: {', '.join(required_missing)}")
            return False
        else:
            print(f"\n✅ All required files found!")
            return True

    def generate_secret_key(self):
        """Generate a secure Django secret key"""
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*(-_=+)"
        secret_key = ''.join(secrets.choice(alphabet) for _ in range(50))
        return secret_key

    def test_django_configuration(self):
        """Test Django configuration"""
        print("\n🧪 Testing Django Configuration...")

        project_path = Path(self.project_dir)
        if not project_path.exists():
            print(f"❌ Project directory not found: {self.project_dir}")
            return False

        # Test Django check command
        try:
            venv_python = project_path / "venv" / "bin" / "python"
            if not venv_python.exists():
                venv_python = "python3.10"  # Fallback to system python

            manage_py = project_path / "manage.py"
            if manage_py.exists():
                # Test basic Django check
                result = subprocess.run([
                    str(venv_python), str(manage_py), "check", "--settings=smsmali.settings"
                ], capture_output=True, text=True, cwd=project_path, timeout=30)

                if result.returncode == 0:
                    print("✅ Django configuration is valid")
                    return True
                else:
                    print(f"❌ Django check failed: {result.stderr}")
                    return False
            else:
                print("❌ manage.py not found")
                return False

        except subprocess.TimeoutExpired:
            print("⚠️ Django check timed out")
            return False
        except Exception as e:
            print(f"⚠️ Could not test Django configuration: {e}")
            return False

    def create_advanced_config_file(self):
        """Create advanced configuration file"""
        config = {
            "webapp_info": {
                "username": self.username,
                "domain": self.domain,
                "project_directory": self.project_dir,
                "python_version": "3.10"
            },
            "paths": {
                "source_code": self.project_dir,
                "wsgi_file": self.wsgi_file,
                "virtualenv": self.venv_dir,
                "static_root": self.static_dir,
                "media_root": self.media_dir
            },
            "static_files_mapping": [
                {
                    "url": "/static/",
                    "directory": self.static_dir,
                    "description": "Django static files"
                },
                {
                    "url": "/media/",
                    "directory": self.media_dir,
                    "description": "User uploaded media files"
                }
            ],
            "environment_variables": {
                "DJANGO_ENV": {
                    "value": "production",
                    "description": "Set Django to production mode"
                },
                "DJANGO_SECRET_KEY": {
                    "value": self.generate_secret_key(),
                    "description": "Django secret key for security"
                },
                "DJANGO_SETTINGS_MODULE": {
                    "value": "smsmali.settings_production",
                    "description": "Use production settings"
                }
            },
            "security_checklist": [
                "Set DEBUG = False in production settings",
                "Configure ALLOWED_HOSTS properly",
                "Set secure SECRET_KEY",
                "Enable HTTPS redirects",
                "Configure CORS settings",
                "Set up proper logging"
            ],
            "deployment_commands": [
                f"cd {self.project_dir}",
                "source venv/bin/activate",
                "python manage.py collectstatic --noinput --settings=smsmali.settings_production",
                "python manage.py migrate --settings=smsmali.settings_production"
            ]
        }

        config_file = Path(self.project_dir) / "pythonanywhere_webapp_config.json"
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)

        print(f"\n✅ Advanced configuration saved to: {config_file}")
        return config

    def print_webapp_config(self):
        """Print the web app configuration instructions"""
        print("\n📋 WEB APP CONFIGURATION INSTRUCTIONS")
        print("=" * 50)
        
        print("\n1. 🌐 WEB APP SETTINGS:")
        print(f"   • Go to: https://www.pythonanywhere.com/user/{self.username}/webapps/")
        print(f"   • Domain: {self.domain}")
        print("   • Python version: 3.10 (recommended)")
        print("   • Framework: Manual configuration")

        print("\n2. 📁 SOURCE CODE:")
        print(f"   • Source code directory: {self.project_dir}")

        print("\n3. 🐍 WSGI CONFIGURATION:")
        print(f"   • WSGI configuration file: {self.wsgi_file}")

        print("\n4. 📂 STATIC FILES MAPPING:")
        print("   Add these static file mappings:")
        print(f"   • URL: /static/")
        print(f"   • Directory: {self.static_dir}")
        print(f"   • URL: /media/")
        print(f"   • Directory: {self.media_dir}")

        print("\n5. 🔧 VIRTUAL ENVIRONMENT:")
        print(f"   • Virtualenv path: {self.venv_dir}")

        print("\n6. 🌍 ENVIRONMENT VARIABLES:")
        print("   Add these environment variables in the Web tab:")
        print("   • DJANGO_ENV = production")
        print("   • DJANGO_SECRET_KEY = [use generated key below]")
        print("   • DJANGO_SETTINGS_MODULE = smsmali.settings_production")

        print("\n7. 🔄 RELOAD WEB APP:")
        print("   • Click the 'Reload' button to apply changes")

        print("\n8. 🔍 VERIFICATION:")
        print(f"   • Test URL: https://{self.domain}")
        print(f"   • Admin URL: https://{self.domain}/admin/")
        print(f"   • API Docs: https://{self.domain}/api/docs/")

    def create_pa_config_file(self):
        """Create a configuration file for PythonAnywhere"""
        config = {
            "webapp_settings": {
                "domain": self.domain,
                "python_version": "3.10",
                "source_code": self.project_dir,
                "wsgi_file": self.wsgi_file,
                "virtualenv": self.venv_dir
            },
            "static_files": [
                {
                    "url": "/static/",
                    "directory": self.static_dir,
                    "description": "Django static files (CSS, JS, images)"
                },
                {
                    "url": "/media/",
                    "directory": self.media_dir,
                    "description": "User uploaded files"
                }
            ],
            "environment_variables": {
                "DJANGO_ENV": "production",
                "DJANGO_SECRET_KEY": self.generate_secret_key(),
                "DJANGO_SETTINGS_MODULE": "smsmali.settings_production"
            },
            "security_notes": [
                "Ensure DEBUG=False in production settings",
                "Set proper ALLOWED_HOSTS",
                "Configure CORS settings for frontend",
                "Use HTTPS in production"
            ]
        }

        config_file = Path(self.project_dir) / "pythonanywhere_config.json"
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)

        print(f"\n✅ Configuration saved to: {config_file}")
        return config

    def print_deployment_checklist(self):
        """Print deployment checklist"""
        print("\n✅ DEPLOYMENT CHECKLIST")
        print("=" * 30)
        
        checklist = [
            "Run deploy_to_pythonanywhere.py script",
            "Create/configure web app on PythonAnywhere",
            "Set WSGI configuration file",
            "Configure static files mapping",
            "Configure media files mapping", 
            "Set virtual environment path",
            "Add environment variables",
            "Generate and set SECRET_KEY",
            "Test the application",
            "Set up custom domain (if needed)"
        ]
        
        for i, item in enumerate(checklist, 1):
            print(f"   {i:2d}. [ ] {item}")

    def generate_secret_key(self):
        """Generate a Django secret key"""
        try:
            from django.core.management.utils import get_random_secret_key
            secret_key = get_random_secret_key()
            print(f"\n🔑 GENERATED SECRET KEY:")
            print(f"   {secret_key}")
            print("\n⚠️  IMPORTANT: Save this key securely and add it to environment variables!")
            return secret_key
        except ImportError:
            print("\n⚠️  Django not available. Please generate a secret key manually.")
            print("   You can use: python -c 'from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())'")
            return None

    def print_troubleshooting(self):
        """Print troubleshooting tips"""
        print("\n🔧 TROUBLESHOOTING TIPS")
        print("=" * 25)
        
        tips = [
            "Check error logs in PythonAnywhere Web tab",
            "Ensure all file paths are correct",
            "Verify virtual environment is activated",
            "Check static files are collected properly",
            "Ensure database migrations are applied",
            "Verify environment variables are set",
            "Check ALLOWED_HOSTS in Django settings",
            "Ensure CORS settings are configured for production"
        ]
        
        for i, tip in enumerate(tips, 1):
            print(f"   {i}. {tip}")

    def print_useful_commands(self):
        """Print useful commands for PythonAnywhere"""
        print("\n💻 USEFUL COMMANDS")
        print("=" * 20)
        
        commands = [
            ("Check Django version", f"cd {self.project_dir} && python manage.py --version"),
            ("Run migrations", f"cd {self.project_dir} && python manage.py migrate"),
            ("Collect static files", f"cd {self.project_dir} && python manage.py collectstatic"),
            ("Create superuser", f"cd {self.project_dir} && python manage.py createsuperuser"),
            ("Check deployment", f"cd {self.project_dir} && python manage.py check --deploy"),
            ("View error logs", "tail -f /var/log/smsmali.pythonanywhere.com.error.log"),
            ("Restart web app", "touch /var/www/smsmali_pythonanywhere_com_wsgi.py")
        ]
        
        for desc, cmd in commands:
            print(f"\n   📌 {desc}:")
            print(f"      {cmd}")

    def print_deployment_readiness(self):
        """Check and display deployment readiness"""
        print("\n🚀 Deployment Readiness Check")
        print("=" * 35)

        checks = [
            ("Project structure validation", self.validate_project_structure),
            ("Django configuration test", self.test_django_configuration),
        ]

        passed_checks = 0
        for description, check_func in checks:
            print(f"\n🔍 {description}...")
            if check_func():
                passed_checks += 1

        readiness_percentage = (passed_checks / len(checks)) * 100
        print(f"\n📊 Deployment Readiness: {readiness_percentage:.0f}% ({passed_checks}/{len(checks)} checks passed)")

        if readiness_percentage == 100:
            print("🎉 Your project is ready for deployment!")
        elif readiness_percentage >= 50:
            print("⚠️ Your project needs some fixes before deployment")
        else:
            print("❌ Your project requires significant work before deployment")

    def print_quick_setup_commands(self):
        """Print quick setup commands for copy-paste"""
        print("\n⚡ Quick Setup Commands (Copy & Paste)")
        print("=" * 45)

        commands = [
            f"# Navigate to project directory",
            f"cd {self.project_dir}",
            "",
            f"# Activate virtual environment",
            f"source venv/bin/activate",
            "",
            f"# Run Django commands",
            f"python manage.py collectstatic --noinput --settings=smsmali.settings_production",
            f"python manage.py migrate --settings=smsmali.settings_production",
            "",
            f"# Test Django configuration",
            f"python manage.py check --settings=smsmali.settings_production",
            f"python manage.py check --deploy --settings=smsmali.settings_production"
        ]

        for cmd in commands:
            print(f"   {cmd}")

    def setup(self, validate=True, generate_config=True):
        """Advanced setup process"""
        print("🚀 Starting advanced web app setup...")

        # Validation phase
        if validate:
            self.print_deployment_readiness()

        # Configuration generation
        if generate_config:
            config = self.create_advanced_config_file()

        # Print configuration instructions
        self.print_webapp_config()

        # Print additional helpful information
        self.print_quick_setup_commands()
        self.print_deployment_checklist()
        self.print_troubleshooting()
        self.print_useful_commands()

        print("\n🎉 Advanced setup completed!")
        print(f"📖 Configuration saved and instructions generated")
        print(f"🌐 Configure your web app at: https://www.pythonanywhere.com/user/{self.username}/webapps/")

        if validate and hasattr(self, 'config'):
            secret_key = config['environment_variables']['DJANGO_SECRET_KEY']['value']
            print(f"\n🔑 Your generated secret key:")
            print(f"   {secret_key}")
            print("   ⚠️ Save this securely and add it to environment variables!")

def main():
    parser = argparse.ArgumentParser(description='Advanced PythonAnywhere Web App Setup')
    parser.add_argument('--auto-detect', action='store_true', default=True,
                       help='Auto-detect project directory')
    parser.add_argument('--validate', action='store_true', default=True,
                       help='Validate project structure and configuration')
    parser.add_argument('--generate-config', action='store_true', default=True,
                       help='Generate advanced configuration file')
    parser.add_argument('--project-dir', type=str,
                       help='Specify project directory manually')

    args = parser.parse_args()

    setup = AdvancedPythonAnywhereWebAppSetup(auto_detect=args.auto_detect)

    # Override auto-detection if manual path provided
    if args.project_dir:
        setup.project_dir = args.project_dir
        setup.static_dir = f"{setup.project_dir}/staticfiles"
        setup.media_dir = f"{setup.project_dir}/media"
        setup.venv_dir = f"{setup.project_dir}/venv"
        setup.wsgi_file = f"{setup.project_dir}/wsgi.py"

    setup.setup(validate=args.validate, generate_config=args.generate_config)

if __name__ == "__main__":
    main()
