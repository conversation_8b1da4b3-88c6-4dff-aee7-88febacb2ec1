"""
WSGI config for smsmali project for production deployment.

This module contains the WSGI application used by Django's runserver
and production WSGI servers like PythonAnywhere.
"""

import os
import sys

# Add the project directory to the Python path
path = '/home/<USER>/smsmali'
if path not in sys.path:
    sys.path.insert(0, path)

# Set the Django settings module for production
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smsmali.settings_production')

from django.core.wsgi import get_wsgi_application

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv(os.path.join(path, '.env'))
except ImportError:
    pass

application = get_wsgi_application()