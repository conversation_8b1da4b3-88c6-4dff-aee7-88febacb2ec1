import { useRef, useEffect } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { useGLTF, PresentationControls, Environment, ContactShadows, Float } from '@react-three/drei'
import { Vector3 } from 'three'

// This component represents a 3D jewelry item (like a gold ring or necklace)
function JewelryModel({ position, rotation, scale }) {
  const modelRef = useRef()
  
  // Simple animation
  useFrame((state) => {
    const t = state.clock.getElapsedTime()
    modelRef.current.rotation.y = Math.sin(t / 4) / 8
    modelRef.current.rotation.z = Math.sin(t / 4) / 8
    modelRef.current.position.y = Math.sin(t / 2) / 10
    modelRef.current.scale.x = modelRef.current.scale.y = modelRef.current.scale.z = 1 + Math.sin(t / 4) / 30
  })

  // Since we don't have an actual model file, we'll create a simple gold ring
  return (
    <group ref={modelRef} position={position} rotation={rotation} scale={scale}>
      <mesh castShadow receiveShadow>
        <torusGeometry args={[1, 0.3, 16, 32]} />
        <meshStandardMaterial 
          color="#F59E0B" 
          metalness={1} 
          roughness={0.2} 
          envMapIntensity={1}
        />
      </mesh>
    </group>
  )
}

// This component represents a 3D watch
function WatchModel({ position, rotation, scale }) {
  const modelRef = useRef()
  
  // Simple animation
  useFrame((state) => {
    const t = state.clock.getElapsedTime()
    modelRef.current.rotation.y = Math.sin(t / 4) / 10 + Math.PI
    modelRef.current.position.y = Math.sin(t / 2) / 15
  })

  // Since we don't have an actual model file, we'll create a simple watch
  return (
    <group ref={modelRef} position={position} rotation={rotation} scale={scale}>
      {/* Watch body */}
      <mesh castShadow receiveShadow>
        <cylinderGeometry args={[1.2, 1.2, 0.3, 32]} />
        <meshStandardMaterial 
          color="#94A3B8" 
          metalness={0.8} 
          roughness={0.2} 
        />
      </mesh>
      
      {/* Watch face */}
      <mesh castShadow receiveShadow position={[0, 0.16, 0]}>
        <cylinderGeometry args={[1.1, 1.1, 0.05, 32]} />
        <meshStandardMaterial 
          color="#FFFFFF" 
          metalness={0.1} 
          roughness={0.2} 
        />
      </mesh>
      
      {/* Watch hands */}
      <mesh position={[0, 0.2, 0]} rotation={[0, 0, Math.PI / 4]}>
        <boxGeometry args={[0.05, 0.8, 0.02]} />
        <meshStandardMaterial color="#000000" />
      </mesh>
      <mesh position={[0, 0.2, 0]} rotation={[0, 0, Math.PI / 2 + Math.PI / 6]}>
        <boxGeometry args={[0.05, 0.6, 0.02]} />
        <meshStandardMaterial color="#000000" />
      </mesh>
    </group>
  )
}

// This component represents a 3D smartphone
function SmartphoneModel({ position, rotation, scale }) {
  const modelRef = useRef()
  
  // Simple animation
  useFrame((state) => {
    const t = state.clock.getElapsedTime()
    modelRef.current.rotation.y = Math.sin(t / 4) / 6
    modelRef.current.position.y = Math.sin(t / 3) / 12
  })

  // Since we don't have an actual model file, we'll create a simple smartphone
  return (
    <group ref={modelRef} position={position} rotation={rotation} scale={scale}>
      {/* Phone body */}
      <mesh castShadow receiveShadow>
        <boxGeometry args={[1.8, 3.6, 0.2]} />
        <meshStandardMaterial 
          color="#1E293B" 
          metalness={0.8} 
          roughness={0.2} 
        />
      </mesh>
      
      {/* Phone screen */}
      <mesh castShadow receiveShadow position={[0, 0, 0.11]}>
        <boxGeometry args={[1.7, 3.4, 0.01]} />
        <meshStandardMaterial 
          color="#0F172A" 
          metalness={0.5} 
          roughness={0.1} 
          emissive="#1E3A8A"
          emissiveIntensity={0.2}
        />
      </mesh>
    </group>
  )
}

// Main component that renders the 3D scene
const HeroModel = () => {
  return (
    <div className="w-full h-full">
      <Canvas shadows camera={{ position: [0, 0, 10], fov: 25 }}>
        <color attach="background" args={['transparent']} />
        <PresentationControls
          global
          rotation={[0, 0, 0]}
          polar={[-Math.PI / 4, Math.PI / 4]}
          azimuth={[-Math.PI / 4, Math.PI / 4]}
          config={{ mass: 2, tension: 400 }}
          snap={{ mass: 4, tension: 300 }}
        >
          <Float rotationIntensity={0.2} floatIntensity={0.5}>
            <JewelryModel 
              position={[-2.5, 0, 0]} 
              rotation={[0.5, 0, 0]} 
              scale={0.8} 
            />
            <WatchModel 
              position={[0, 0, 0]} 
              rotation={[Math.PI / 2, 0, 0]} 
              scale={0.8} 
            />
            <SmartphoneModel 
              position={[2.5, 0, 0]} 
              rotation={[0, 0, 0]} 
              scale={0.5} 
            />
          </Float>
        </PresentationControls>
        <Environment preset="city" />
        <ContactShadows 
          position={[0, -2, 0]} 
          opacity={0.5} 
          scale={10} 
          blur={1.5} 
          far={4} 
        />
      </Canvas>
    </div>
  )
}

export default HeroModel