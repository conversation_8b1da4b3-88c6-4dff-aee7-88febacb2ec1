import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { FaQuoteLeft, FaStar } from 'react-icons/fa'

// Mock data for testimonials
const testimonials = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'Regular Customer',
    image: 'https://randomuser.me/api/portraits/women/1.jpg',
    rating: 5,
    text: 'SMSMali offered me the best rate for my gold jewelry when I needed quick cash. The process was smooth, transparent, and I was treated with respect. Highly recommended!',
  },
  {
    id: 2,
    name: '<PERSON>',
    role: 'Business Owner',
    image: 'https://randomuser.me/api/portraits/men/2.jpg',
    rating: 5,
    text: 'I purchased a pre-owned laptop from SMSMali at an incredible price. It works perfectly and came with a warranty. The staff was knowledgeable and helped me find exactly what I needed.',
  },
  {
    id: 3,
    name: '<PERSON><PERSON><PERSON>',
    role: 'Regular Customer',
    image: 'https://randomuser.me/api/portraits/men/3.jpg',
    rating: 4,
    text: 'When I needed a loan quickly, SMSMali was there for me. The interest rates were fair, and the terms were clearly explained. I've used their services multiple times now.',
  },
  {
    id: 4,
    name: '<PERSON><PERSON>',
    role: 'First-time Customer',
    image: 'https://randomuser.me/api/portraits/women/4.jpg',
    rating: 5,
    text: 'I was nervous about using a pawnshop, but SMSMali made me feel comfortable. They gave me a fair appraisal for my watch and the loan process was quick and easy.',
  },
  {
    id: 5,
    name: '<PERSON>',
    role: 'Collector',
    image: 'https://randomuser.me/api/portraits/men/5.jpg',
    rating: 5,
    text: 'I found a rare vintage watch at SMSMali that I'd been searching for. The condition was excellent and the price was fair. The staff's knowledge about collectibles is impressive.',
  },
]

const Testimonials = () => {
  const [activeIndex, setActiveIndex] = useState(0)
  const [visibleTestimonials, setVisibleTestimonials] = useState([])

  useEffect(() => {
    // Determine how many testimonials to show based on screen size
    const handleResize = () => {
      const width = window.innerWidth
      if (width >= 1024) { // lg screens
        setVisibleTestimonials(testimonials.slice(0, 3))
      } else if (width >= 768) { // md screens
        setVisibleTestimonials(testimonials.slice(0, 2))
      } else { // sm screens
        setVisibleTestimonials([testimonials[activeIndex]])
      }
    }

    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [activeIndex])

  const nextTestimonial = () => {
    setActiveIndex((prevIndex) => (prevIndex + 1) % testimonials.length)
  }

  const prevTestimonial = () => {
    setActiveIndex((prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length)
  }

  const renderStars = (rating) => {
    return Array.from({ length: 5 }).map((_, index) => (
      <FaStar 
        key={index} 
        className={index < rating ? 'text-yellow-500' : 'text-gray-300'} 
      />
    ))
  }

  return (
    <section className="py-20 bg-base-100">
      <div className="container mx-auto px-4">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            What Our <span className="text-primary">Clients Say</span>
          </h2>
          <p className="text-neutral max-w-2xl mx-auto">
            Don't just take our word for it. Here's what our satisfied customers have to say about their experience with SMSMali.
          </p>
        </motion.div>

        {/* Mobile Testimonial Carousel (visible on small screens) */}
        <div className="block md:hidden">
          <div className="relative">
            <motion.div 
              key={activeIndex}
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -50 }}
              transition={{ duration: 0.5 }}
              className="card bg-base-100 shadow-lg p-6"
            >
              <div className="flex flex-col items-center text-center">
                <div className="avatar mb-4">
                  <div className="w-20 h-20 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2">
                    <img src={testimonials[activeIndex].image} alt={testimonials[activeIndex].name} />
                  </div>
                </div>
                <FaQuoteLeft className="text-3xl text-primary opacity-20 mb-4" />
                <p className="text-neutral mb-6">{testimonials[activeIndex].text}</p>
                <div className="flex mb-2">
                  {renderStars(testimonials[activeIndex].rating)}
                </div>
                <h3 className="font-bold">{testimonials[activeIndex].name}</h3>
                <p className="text-sm text-neutral">{testimonials[activeIndex].role}</p>
              </div>
            </motion.div>

            {/* Navigation Buttons */}
            <div className="flex justify-center mt-6 space-x-4">
              <button 
                onClick={prevTestimonial} 
                className="btn btn-circle btn-sm btn-outline"
                aria-label="Previous testimonial"
              >
                ❮
              </button>
              <button 
                onClick={nextTestimonial} 
                className="btn btn-circle btn-sm btn-outline"
                aria-label="Next testimonial"
              >
                ❯
              </button>
            </div>
          </div>
        </div>

        {/* Desktop Testimonials Grid (visible on medium and large screens) */}
        <div className="hidden md:grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {visibleTestimonials.map((testimonial, index) => (
            <motion.div 
              key={testimonial.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, margin: "-100px" }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="card bg-base-100 shadow-lg p-6 hover:shadow-xl transition-shadow duration-300"
            >
              <div className="flex flex-col items-center text-center">
                <div className="avatar mb-4">
                  <div className="w-20 h-20 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2">
                    <img src={testimonial.image} alt={testimonial.name} />
                  </div>
                </div>
                <FaQuoteLeft className="text-3xl text-primary opacity-20 mb-4" />
                <p className="text-neutral mb-6">{testimonial.text}</p>
                <div className="flex mb-2">
                  {renderStars(testimonial.rating)}
                </div>
                <h3 className="font-bold">{testimonial.name}</h3>
                <p className="text-sm text-neutral">{testimonial.role}</p>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Testimonials