import { motion } from 'framer-motion'
import { FaUsers, FaHandshake, FaStar, FaMoneyBillWave } from 'react-icons/fa'

const stats = [
  {
    id: 1,
    icon: <FaUsers className="text-4xl text-secondary" />,
    value: '5000+',
    label: 'Happy Clients',
    description: 'Trusted by thousands of satisfied customers across Midrand and beyond.',
  },
  {
    id: 2,
    icon: <FaHandshake className="text-4xl text-secondary" />,
    value: '10000+',
    label: 'Transactions',
    description: 'Successfully completed transactions with complete transparency and trust.',
  },
  {
    id: 3,
    icon: <FaStar className="text-4xl text-secondary" />,
    value: '4.9',
    label: 'Average Rating',
    description: 'Consistently rated as the top pawnshop in Midrand by our customers.',
  },
  {
    id: 4,
    icon: <FaMoneyBillWave className="text-4xl text-secondary" />,
    value: 'R10M+',
    label: 'Loans Provided',
    description: 'Helping our community with financial solutions when they need it most.',
  },
]

const StatsSection = () => {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Our <span className="text-primary">Impact</span> in Numbers
          </h2>
          <p className="text-neutral max-w-2xl mx-auto">
            SMSMali has been serving the Midrand community with excellence and integrity. 
            Here's a glimpse of our journey so far.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <motion.div 
              key={stat.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, margin: "-100px" }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="bg-white p-8 rounded-lg shadow-md text-center card-hover"
            >
              <div className="flex justify-center mb-4">
                {stat.icon}
              </div>
              <h3 className="text-3xl font-bold text-primary mb-2">{stat.value}</h3>
              <h4 className="text-lg font-semibold mb-2">{stat.label}</h4>
              <p className="text-neutral">{stat.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default StatsSection