import { motion } from 'framer-motion'
import { Helmet } from 'react-helmet'
import { FaMoneyBillWave, FaGem, FaMobileAlt, FaLaptop, FaHandHoldingUsd, FaSearch, FaTools, FaShieldAlt } from 'react-icons/fa'

const ServicesPage = () => {
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6 }
    }
  }

  const staggerContainer = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: 0.2
      }
    }
  }

  const services = [
    {
      id: 1,
      title: "Pawn Loans",
      icon: <FaMoneyBillWave className="text-5xl text-primary mb-4" />,
      description: "Get quick cash with our collateral-based loans. Bring in your valuables and walk out with money in minutes. We offer competitive rates and flexible repayment terms.",
      features: [
        "Instant cash loans against your valuables",
        "Competitive interest rates",
        "Flexible repayment terms",
        "No credit checks required",
        "Confidential and secure process"
      ]
    },
    {
      id: 2,
      title: "Jewelry Buying & Selling",
      icon: <FaGem className="text-5xl text-primary mb-4" />,
      description: "Whether you're looking to sell your jewelry or find unique pieces at great prices, our jewelry experts provide fair valuations and a wide selection of quality items.",
      features: [
        "Expert appraisal of gold, silver, diamonds, and precious stones",
        "Competitive buying prices",
        "Curated selection of pre-owned jewelry",
        "Authentication services",
        "Jewelry cleaning and minor repairs"
      ]
    },
    {
      id: 3,
      title: "Electronics Trading",
      icon: <FaMobileAlt className="text-5xl text-primary mb-4" />,
      description: "Trade in your smartphones, tablets, laptops, and other electronics for cash or find quality pre-owned devices at a fraction of retail prices.",
      features: [
        "Fair market value for your devices",
        "Data wiping services",
        "Quality-tested pre-owned electronics",
        "30-day warranty on purchased items",
        "Trade-in options available"
      ]
    },
    {
      id: 4,
      title: "Luxury Watch Services",
      icon: <FaLaptop className="text-5xl text-primary mb-4" />,
      description: "Our watch specialists can help you buy, sell, or pawn luxury timepieces. We deal with brands like Rolex, Omega, Tag Heuer, and more.",
      features: [
        "Authentication and valuation",
        "Competitive prices for luxury watches",
        "Curated selection of pre-owned timepieces",
        "Watch maintenance advice",
        "Special loans for high-value watches"
      ]
    },
    {
      id: 5,
      title: "Gold Buying",
      icon: <FaHandHoldingUsd className="text-5xl text-primary mb-4" />,
      description: "Convert your gold items into cash with our gold buying service. We offer competitive rates based on current market prices for gold jewelry, coins, and bullion.",
      features: [
        "Free evaluation and weighing",
        "Prices updated daily based on market rates",
        "Immediate cash payment",
        "All karats and forms of gold accepted",
        "No quantity too small or large"
      ]
    },
    {
      id: 6,
      title: "Appraisal Services",
      icon: <FaSearch className="text-5xl text-primary mb-4" />,
      description: "Get professional appraisals for your valuables, whether for insurance purposes, estate planning, or just to know what your items are worth.",
      features: [
        "Detailed written appraisals",
        "Gemological assessments",
        "Electronics valuation",
        "Antiques and collectibles appraisal",
        "Quick turnaround time"
      ]
    },
    {
      id: 7,
      title: "Repair Services",
      icon: <FaTools className="text-5xl text-primary mb-4" />,
      description: "Our skilled technicians offer repair services for jewelry, watches, and select electronics to keep your valuables in perfect condition.",
      features: [
        "Jewelry cleaning and polishing",
        "Chain and clasp repairs",
        "Stone setting and replacement",
        "Watch battery replacement",
        "Basic electronics troubleshooting"
      ]
    },
    {
      id: 8,
      title: "Secure Storage",
      icon: <FaShieldAlt className="text-5xl text-primary mb-4" />,
      description: "Keep your valuables safe with our secure storage options. Perfect for items you don't use regularly but want to keep protected.",
      features: [
        "Climate-controlled environment",
        "24/7 surveillance and security",
        "Insurance coverage available",
        "Flexible access options",
        "Competitive monthly rates"
      ]
    }
  ]

  return (
    <>
      <Helmet>
        <title>Services - SMSMali Pawnshop</title>
        <meta name="description" content="Explore SMSMali's comprehensive services including pawn loans, jewelry buying and selling, electronics trading, appraisals, and more." />
      </Helmet>

      {/* Hero Section */}
      <section className="relative bg-gradient-to-b from-primary/10 to-base-100 py-20 md:py-28">
        <div className="container mx-auto px-4">
          <motion.div 
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="max-w-4xl mx-auto text-center"
          >
            <motion.h1 
              variants={fadeIn}
              className="text-4xl md:text-5xl font-bold mb-6 text-gradient-primary"
            >
              Our Services
            </motion.h1>
            <motion.p 
              variants={fadeIn}
              className="text-lg md:text-xl mb-8 text-base-content/80"
            >
              Comprehensive pawnshop services tailored to meet your needs
            </motion.p>
          </motion.div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="py-16 bg-base-100">
        <div className="container mx-auto px-4">
          <motion.div 
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {services.map((service) => (
              <motion.div 
                key={service.id}
                variants={fadeIn}
                className="bg-base-200 rounded-lg p-8 shadow-md hover:shadow-xl transition-all duration-300 flex flex-col h-full"
              >
                <div className="flex justify-center">
                  {service.icon}
                </div>
                <h3 className="text-2xl font-bold mb-4 text-center">{service.title}</h3>
                <p className="mb-6 text-base-content/80 flex-grow">{service.description}</p>
                <a 
                  href={`#service-${service.id}`} 
                  className="btn btn-primary btn-outline w-full"
                >
                  Learn More
                </a>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Detailed Service Sections */}
      {services.map((service) => (
        <section 
          key={service.id} 
          id={`service-${service.id}`}
          className={`py-16 ${service.id % 2 === 0 ? 'bg-base-100' : 'bg-base-200'}`}
        >
          <div className="container mx-auto px-4">
            <motion.div 
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, margin: "-100px" }}
              variants={staggerContainer}
              className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center"
            >
              <motion.div 
                variants={fadeIn}
                className={`order-2 ${service.id % 2 === 0 ? 'md:order-1' : 'md:order-2'}`}
              >
                <h2 className="text-3xl font-bold mb-6">{service.title}</h2>
                <p className="mb-6 text-base-content/80">{service.description}</p>
                <ul className="space-y-3">
                  {service.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-primary mr-2">✓</span>
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
                <div className="mt-8">
                  <a href="/contact" className="btn btn-primary mr-4">Contact Us</a>
                  <a href="#" className="btn btn-outline">Get a Quote</a>
                </div>
              </motion.div>
              <motion.div 
                variants={fadeIn}
                className={`flex justify-center items-center order-1 ${service.id % 2 === 0 ? 'md:order-2' : 'md:order-1'}`}
              >
                <div className="bg-primary/10 p-12 rounded-full">
                  <div className="text-8xl text-primary">
                    {service.icon}
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </section>
      ))}

      {/* FAQ Section */}
      <section className="py-16 bg-base-100">
        <div className="container mx-auto px-4">
          <motion.div 
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="max-w-3xl mx-auto"
          >
            <motion.h2 
              variants={fadeIn}
              className="text-3xl font-bold mb-8 text-center"
            >
              Frequently Asked Questions
            </motion.h2>

            <div className="space-y-4">
              {[
                {
                  question: "How does the pawn loan process work?",
                  answer: "The process is simple: bring in your item for a free evaluation, we'll make you an offer based on its value, and if you accept, you'll receive cash immediately. You'll get a pawn ticket with all the details including the repayment date. Repay the loan plus interest by the due date to retrieve your item."
                },
                {
                  question: "What items can I pawn or sell?",
                  answer: "We accept a wide range of items including jewelry, watches, electronics, musical instruments, tools, and more. Items should be in good working condition and have resale value."
                },
                {
                  question: "How do you determine the value of my items?",
                  answer: "Our expert appraisers consider factors like condition, age, brand, market demand, and current resale value. For precious metals, we consider weight, purity, and current market prices."
                },
                {
                  question: "What happens if I can't repay my loan on time?",
                  answer: "If you need more time, contact us before the due date to discuss extension options. If you cannot repay the loan, the item becomes the property of the pawnshop and may be sold."
                },
                {
                  question: "Do you offer any warranty on items purchased from your shop?",
                  answer: "Yes, we offer a 30-day warranty on most electronics and a 7-day guarantee on other items to ensure they're in working condition as described at the time of purchase."
                }
              ].map((faq, index) => (
                <motion.div 
                  key={index}
                  variants={fadeIn}
                  className="collapse collapse-plus bg-base-200"
                >
                  <input type="radio" name="faq-accordion" /> 
                  <div className="collapse-title text-xl font-medium">
                    {faq.question}
                  </div>
                  <div className="collapse-content"> 
                    <p>{faq.answer}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary text-primary-content">
        <div className="container mx-auto px-4 text-center">
          <motion.div 
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="max-w-3xl mx-auto"
          >
            <motion.h2 
              variants={fadeIn}
              className="text-3xl font-bold mb-6"
            >
              Ready to Get Started?
            </motion.h2>
            <motion.p 
              variants={fadeIn}
              className="text-lg mb-8"
            >
              Visit our store today or contact us to learn more about our services.
            </motion.p>
            <motion.div variants={fadeIn}>
              <a href="/contact" className="btn btn-secondary btn-lg mr-4">Contact Us</a>
              <a href="/loan-calculator" className="btn btn-outline btn-lg border-white text-white hover:bg-white hover:text-primary">Loan Calculator</a>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </>
  )
}

export default ServicesPage