#!/usr/bin/env python
import os
import sys
import subprocess
import webbrowser
from time import sleep

def run_django_server():
    print("Starting Django development server...")
    django_process = subprocess.Popen([sys.executable, 'manage.py', 'runserver'])
    return django_process

def run_react_server():
    print("Starting React development server...")
    os.chdir('frontend')
    npm_process = subprocess.Popen(['npm', 'run', 'dev'])
    os.chdir('..')
    return npm_process

def main():
    # Check if virtual environment is activated
    if not os.environ.get('VIRTUAL_ENV'):
        print("Warning: Virtual environment not detected. It's recommended to activate it first.")
        response = input("Continue anyway? (y/n): ")
        if response.lower() != 'y':
            print("Exiting. Please activate the virtual environment and try again.")
            return
    
    try:
        # Start Django server
        django_process = run_django_server()
        print("Django server started at http://127.0.0.1:8000/")
        
        # Wait a moment for Django to start
        sleep(2)
        
        # Start React server
        react_process = run_react_server()
        print("React server starting...")
        
        # Wait for React to start
        sleep(5)
        
        # Open browsers
        print("Opening browsers...")
        webbrowser.open('http://127.0.0.1:8000/admin/')
        webbrowser.open('http://localhost:5173/')
        
        print("\nDevelopment servers are running!")
        print("Django API: http://127.0.0.1:8000/")
        print("React Frontend: http://localhost:5173/")
        print("\nPress Ctrl+C to stop the servers...")
        
        # Keep the script running until interrupted
        django_process.wait()
        react_process.wait()
        
    except KeyboardInterrupt:
        print("\nStopping servers...")
        django_process.terminate()
        react_process.terminate()
        print("Servers stopped.")
    except Exception as e:
        print(f"Error: {e}")
        if 'django_process' in locals():
            django_process.terminate()
        if 'react_process' in locals():
            react_process.terminate()

if __name__ == "__main__":
    main()