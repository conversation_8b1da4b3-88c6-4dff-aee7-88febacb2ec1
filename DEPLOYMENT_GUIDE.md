# SMSMali PythonAnywhere Deployment Guide

This guide will help you deploy the SMSMali Django + React application to PythonAnywhere.

## 🚀 Quick Start

### Prerequisites
- PythonAnywhere account (username: `smsmali`)
- Git repository with your code
- Basic familiarity with command line

### Step 1: Upload Code to PythonAnywhere

1. **Login to PythonAnywhere Console**
   ```bash
   # Clone your repository
   git clone https://github.com/your-username/smsmali.git
   cd smsmali
   ```

2. **Run the Deployment Script**
   ```bash
   # For development deployment
   python deploy_to_pythonanywhere.py
   
   # For production deployment
   python deploy_to_pythonanywhere.py --production
   ```

### Step 2: Configure Web App

1. **Run the Setup Script**
   ```bash
   python setup_pythonanywhere_webapp.py
   ```

2. **Follow the Generated Instructions**
   - The script will provide detailed configuration steps
   - Copy the generated secret key
   - Configure the web app settings

### Step 3: Configure PythonAnywhere Web App

1. **Go to Web Tab**
   - Visit: https://www.pythonanywhere.com/user/smsmali/webapps/
   - Create a new web app or configure existing one

2. **Set Configuration**
   - **Source code**: `/home/<USER>/smsmali`
   - **WSGI file**: `/home/<USER>/smsmali/wsgi.py`
   - **Virtual environment**: `/home/<USER>/smsmali/venv`

3. **Configure Static Files**
   - URL: `/static/` → Directory: `/home/<USER>/smsmali/staticfiles`
   - URL: `/media/` → Directory: `/home/<USER>/smsmali/media`

4. **Set Environment Variables**
   - `DJANGO_ENV` = `production`
   - `DJANGO_SECRET_KEY` = `your-generated-secret-key`

5. **Reload Web App**

## 📁 Project Structure

```
smsmali/
├── deploy_to_pythonanywhere.py    # Main deployment script
├── setup_pythonanywhere_webapp.py # Web app configuration helper
├── requirements_production.txt     # Production dependencies
├── DEPLOYMENT_GUIDE.md            # This guide
├── frontend/                      # React frontend
├── smsmali/                       # Django project
├── products/                      # Products app
├── loans/                         # Loans app
├── users/                         # Users app
├── static/                        # Static files
├── media/                         # Media files
└── templates/                     # Django templates
```

## 🔧 Manual Configuration Steps

If you prefer manual setup:

### 1. Create Virtual Environment
```bash
cd /home/<USER>
python3 -m venv smsmali/venv
source smsmali/venv/bin/activate
pip install -r smsmali/requirements_production.txt
```

### 2. Build Frontend (if Node.js available)
```bash
cd smsmali/frontend
npm install
npm run build
cp -r dist/* ../static/frontend/
```

### 3. Configure Django
```bash
cd /home/<USER>/smsmali
python manage.py collectstatic --noinput
python manage.py migrate
python manage.py createsuperuser
```

### 4. Create WSGI File
Create `/home/<USER>/smsmali/wsgi.py`:
```python
import os
import sys

path = '/home/<USER>/smsmali'
if path not in sys.path:
    sys.path.insert(0, path)

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smsmali.settings_production')

from django.core.wsgi import get_wsgi_application
application = get_wsgi_application()
```

## 🌐 Frontend Deployment

The React frontend is built and served as static files through Django. The build process:

1. Runs `npm run build` in the frontend directory
2. Copies built files to Django's static directory
3. Django serves the frontend through static file handling

## 🔒 Security Considerations

### Production Settings
- `DEBUG = False`
- Proper `ALLOWED_HOSTS` configuration
- Secure `SECRET_KEY` from environment variables
- HTTPS redirects enabled
- CORS properly configured

### Environment Variables
Set these in PythonAnywhere Web tab:
- `DJANGO_ENV=production`
- `DJANGO_SECRET_KEY=your-secret-key`

## 🐛 Troubleshooting

### Common Issues

1. **Import Errors**
   - Check virtual environment is activated
   - Verify all dependencies are installed

2. **Static Files Not Loading**
   - Run `python manage.py collectstatic`
   - Check static file mappings in Web tab

3. **Database Errors**
   - Run `python manage.py migrate`
   - Check database file permissions

4. **CORS Errors**
   - Verify CORS settings in production settings
   - Check allowed origins

### Useful Commands

```bash
# Check deployment readiness
python manage.py check --deploy

# View error logs
tail -f /var/log/smsmali.pythonanywhere.com.error.log

# Restart web app
touch /var/www/smsmali_pythonanywhere_com_wsgi.py

# Test Django shell
python manage.py shell
```

## 📞 Support

If you encounter issues:

1. Check the error logs in PythonAnywhere Web tab
2. Review the troubleshooting section above
3. Consult PythonAnywhere documentation
4. Contact PythonAnywhere support if needed

## 🔄 Updates and Redeployment

To update your application:

1. **Pull latest changes**
   ```bash
   cd /home/<USER>/smsmali
   git pull origin main
   ```

2. **Run deployment script**
   ```bash
   python deploy_to_pythonanywhere.py --production
   ```

3. **Reload web app** in PythonAnywhere Web tab

## 📈 Performance Tips

1. **Enable caching** in Django settings
2. **Optimize static files** with compression
3. **Use CDN** for static assets (optional)
4. **Monitor application** performance
5. **Regular database maintenance**

---

🎉 **Congratulations!** Your SMSMali application should now be live at:
**https://smsmali.pythonanywhere.com**
