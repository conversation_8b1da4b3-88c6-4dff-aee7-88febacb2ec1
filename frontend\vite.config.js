import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
      },
    },
  },
  optimizeDeps: {
    exclude: ['hls.js'],
    include: ['@react-three/drei', '@react-three/fiber']
  },
  define: {
    global: 'globalThis',
  },
  build: {
    rollupOptions: {
      external: ['hls.js']
    }
  }
})