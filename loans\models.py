from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.conf import settings

class ItemType(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    loan_to_value_ratio = models.DecimalField(
        max_digits=3, 
        decimal_places=2, 
        validators=[MinValueValidator(0.1), MaxValueValidator(0.9)],
        help_text="Percentage of item value that can be loaned (e.g., 0.7 for 70%)"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.name

class LoanTerm(models.Model):
    days = models.PositiveIntegerField()
    interest_rate = models.DecimalField(
        max_digits=4, 
        decimal_places=2, 
        validators=[MinValueValidator(0.01), MaxValueValidator(0.5)],
        help_text="Interest rate for the term (e.g., 0.05 for 5%)"
    )
    description = models.CharField(max_length=100, blank=True)
    is_active = models.BooleanField(default=True)
    
    def __str__(self):
        return f"{self.days} days ({self.interest_rate_percentage}% interest)"
    
    @property
    def interest_rate_percentage(self):
        return self.interest_rate * 100

class Loan(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Pending Approval'),
        ('active', 'Active'),
        ('paid', 'Paid'),
        ('defaulted', 'Defaulted'),
        ('extended', 'Extended'),
    ]
    
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='loans')
    item_type = models.ForeignKey(ItemType, on_delete=models.PROTECT)
    item_description = models.TextField()
    item_value = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    loan_amount = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    loan_term = models.ForeignKey(LoanTerm, on_delete=models.PROTECT)
    interest_amount = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    total_repayment = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    start_date = models.DateField(null=True, blank=True)
    due_date = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Loan #{self.id} - {self.item_description} ({self.status})"

class LoanExtension(models.Model):
    loan = models.ForeignKey(Loan, on_delete=models.CASCADE, related_name='extensions')
    extension_date = models.DateField()
    previous_due_date = models.DateField()
    new_due_date = models.DateField()
    extension_fee = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"Extension for Loan #{self.loan.id} on {self.extension_date}"

class Payment(models.Model):
    PAYMENT_TYPE_CHOICES = [
        ('full', 'Full Payment'),
        ('partial', 'Partial Payment'),
        ('extension', 'Extension Fee'),
    ]
    
    loan = models.ForeignKey(Loan, on_delete=models.CASCADE, related_name='payments')
    amount = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    payment_date = models.DateField()
    payment_type = models.CharField(max_length=20, choices=PAYMENT_TYPE_CHOICES)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.payment_type} payment of {self.amount} for Loan #{self.loan.id}"

class LoanCalculation(models.Model):
    item_type = models.ForeignKey(ItemType, on_delete=models.CASCADE)
    item_value = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    loan_term = models.ForeignKey(LoanTerm, on_delete=models.CASCADE)
    loan_amount = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    interest_amount = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    total_repayment = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    created_at = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    
    def __str__(self):
        return f"Calculation for {self.item_type.name} worth {self.item_value}"