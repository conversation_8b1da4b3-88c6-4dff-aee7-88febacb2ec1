<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SMSMALI API Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #3b82f6;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }
        h2 {
            color: #4b5563;
            margin-top: 30px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 5px;
        }
        h3 {
            color: #6b7280;
            margin-top: 20px;
        }
        .endpoint {
            background-color: #f9fafb;
            border-left: 4px solid #3b82f6;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 4px 4px 0;
        }
        .method {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
            margin-right: 10px;
            min-width: 60px;
            text-align: center;
        }
        .get {
            background-color: #10b981;
            color: white;
        }
        .post {
            background-color: #3b82f6;
            color: white;
        }
        .put {
            background-color: #f59e0b;
            color: white;
        }
        .delete {
            background-color: #ef4444;
            color: white;
        }
        .url {
            font-family: monospace;
            font-size: 1.1em;
        }
        .params {
            margin-top: 10px;
        }
        .param-name {
            font-weight: bold;
        }
        .param-type {
            color: #6b7280;
            font-style: italic;
        }
        .response {
            background-color: #f3f4f6;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            margin-top: 10px;
        }
        pre {
            margin: 0;
        }
        code {
            font-family: 'Courier New', Courier, monospace;
        }
        .auth-required {
            background-color: #fee2e2;
            border-left: 4px solid #ef4444;
            padding: 5px 10px;
            margin: 5px 0;
            border-radius: 0 4px 4px 0;
            font-size: 0.9em;
        }
        .auth-not-required {
            background-color: #ecfdf5;
            border-left: 4px solid #10b981;
            padding: 5px 10px;
            margin: 5px 0;
            border-radius: 0 4px 4px 0;
            font-size: 0.9em;
        }
        .nav {
            background-color: #f9fafb;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .nav ul {
            list-style-type: none;
            padding: 0;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .nav a {
            text-decoration: none;
            color: #3b82f6;
            padding: 5px 10px;
            border-radius: 4px;
        }
        .nav a:hover {
            background-color: #e5e7eb;
        }
    </style>
</head>
<body>
    <h1>SMSMALI API Documentation</h1>
    
    <div class="nav">
        <ul>
            <li><a href="#products">Products</a></li>
            <li><a href="#loans">Loans</a></li>
            <li><a href="#users">Users</a></li>
        </ul>
    </div>
    
    <h2 id="products">Products API</h2>
    
    <div class="endpoint">
        <span class="method get">GET</span>
        <span class="url">/api/products/</span>
        <div class="auth-not-required">Authentication: Not Required</div>
        <p>Get a list of all products with pagination.</p>
        
        <div class="params">
            <h4>Query Parameters:</h4>
            <p><span class="param-name">page</span> <span class="param-type">(integer)</span>: Page number</p>
            <p><span class="param-name">search</span> <span class="param-type">(string)</span>: Search term</p>
            <p><span class="param-name">category</span> <span class="param-type">(string)</span>: Filter by category slug</p>
            <p><span class="param-name">min_price</span> <span class="param-type">(decimal)</span>: Minimum price</p>
            <p><span class="param-name">max_price</span> <span class="param-type">(decimal)</span>: Maximum price</p>
            <p><span class="param-name">ordering</span> <span class="param-type">(string)</span>: Order by field (e.g., price, -price, name)</p>
        </div>
        
        <div class="response">
            <pre><code>{
  "count": 100,
  "next": "http://example.com/api/products/?page=2",
  "previous": null,
  "results": [
    {
      "id": 1,
      "name": "Gold Chain Necklace",
      "slug": "gold-chain-necklace",
      "description": "18K gold chain necklace, 20 inches long",
      "price": "1200.00",
      "discount_price": "1080.00",
      "category": {
        "id": 1,
        "name": "Jewelry",
        "slug": "jewelry"
      },
      "condition": "Like New",
      "stock": 2,
      "primary_image": "/media/products/placeholder.svg",
      "average_rating": 4.5
    },
    // More products...
  ]
}</code></pre>
        </div>
    </div>
    
    <div class="endpoint">
        <span class="method get">GET</span>
        <span class="url">/api/products/{id}/</span>
        <div class="auth-not-required">Authentication: Not Required</div>
        <p>Get detailed information about a specific product.</p>
        
        <div class="response">
            <pre><code>{
  "id": 1,
  "name": "Gold Chain Necklace",
  "slug": "gold-chain-necklace",
  "description": "18K gold chain necklace, 20 inches long",
  "price": "1200.00",
  "discount_price": "1080.00",
  "category": {
    "id": 1,
    "name": "Jewelry",
    "slug": "jewelry"
  },
  "condition": "Like New",
  "stock": 2,
  "is_featured": true,
  "created_at": "2023-11-15T12:00:00Z",
  "updated_at": "2023-11-15T12:00:00Z",
  "images": [
    {
      "id": 1,
      "image": "/media/products/placeholder.svg",
      "is_primary": true
    }
  ],
  "specifications": [
    {
      "id": 1,
      "name": "Material",
      "value": "18K Gold"
    },
    {
      "id": 2,
      "name": "Length",
      "value": "20 inches"
    },
    {
      "id": 3,
      "name": "Weight",
      "value": "15 grams"
    }
  ],
  "reviews": [
    {
      "id": 1,
      "user": "john_doe",
      "rating": 5,
      "comment": "Excellent quality and fast shipping!",
      "created_at": "2023-11-16T10:30:00Z"
    }
  ],
  "average_rating": 4.5
}</code></pre>
        </div>
    </div>
    
    <div class="endpoint">
        <span class="method get">GET</span>
        <span class="url">/api/products/categories/</span>
        <div class="auth-not-required">Authentication: Not Required</div>
        <p>Get a list of all product categories.</p>
        
        <div class="response">
            <pre><code>[
  {
    "id": 1,
    "name": "Jewelry",
    "slug": "jewelry",
    "description": "Fine jewelry including gold, silver, and diamond pieces"
  },
  {
    "id": 2,
    "name": "Electronics",
    "slug": "electronics",
    "description": "Smartphones, laptops, tablets, and other electronic devices"
  },
  // More categories...
]</code></pre>
        </div>
    </div>
    
    <div class="endpoint">
        <span class="method post">POST</span>
        <span class="url">/api/products/{id}/reviews/</span>
        <div class="auth-required">Authentication: Required</div>
        <p>Submit a review for a product.</p>
        
        <div class="params">
            <h4>Request Body:</h4>
            <pre><code>{
  "rating": 5,
  "comment": "Excellent quality and fast shipping!"
}</code></pre>
        </div>
        
        <div class="response">
            <pre><code>{
  "id": 1,
  "user": "john_doe",
  "rating": 5,
  "comment": "Excellent quality and fast shipping!",
  "created_at": "2023-11-16T10:30:00Z"
}</code></pre>
        </div>
    </div>
    
    <h2 id="loans">Loans API</h2>
    
    <div class="endpoint">
        <span class="method get">GET</span>
        <span class="url">/api/loans/item-types/</span>
        <div class="auth-not-required">Authentication: Not Required</div>
        <p>Get a list of all loan item types.</p>
        
        <div class="response">
            <pre><code>[
  {
    "id": 1,
    "name": "Gold Jewelry",
    "description": "Gold rings, necklaces, bracelets",
    "loan_to_value": 80
  },
  {
    "id": 2,
    "name": "Silver Jewelry",
    "description": "Silver rings, necklaces, bracelets",
    "loan_to_value": 70
  },
  // More item types...
]</code></pre>
        </div>
    </div>
    
    <div class="endpoint">
        <span class="method get">GET</span>
        <span class="url">/api/loans/loan-terms/</span>
        <div class="auth-not-required">Authentication: Not Required</div>
        <p>Get a list of all loan terms.</p>
        
        <div class="response">
            <pre><code>[
  {
    "id": 1,
    "months": 1,
    "interest_rate": 5.0,
    "description": "1 month term with 5% interest"
  },
  {
    "id": 2,
    "months": 3,
    "interest_rate": 12.0,
    "description": "3 month term with 12% interest"
  },
  // More loan terms...
]</code></pre>
        </div>
    </div>
    
    <div class="endpoint">
        <span class="method post">POST</span>
        <span class="url">/api/loans/calculate/</span>
        <div class="auth-not-required">Authentication: Not Required</div>
        <p>Calculate loan amount based on item type, value, and term.</p>
        
        <div class="params">
            <h4>Request Body:</h4>
            <pre><code>{
  "item_type": 1,
  "item_value": 1000,
  "loan_term": 3
}</code></pre>
        </div>
        
        <div class="response">
            <pre><code>{
  "id": 1,
  "item_type": {
    "id": 1,
    "name": "Gold Jewelry",
    "loan_to_value": 80
  },
  "loan_term": {
    "id": 2,
    "months": 3,
    "interest_rate": 12.0
  },
  "item_value": "1000.00",
  "loan_amount": "800.00",
  "interest_amount": "96.00",
  "total_repayment": "896.00",
  "loan_to_value_percentage": "80.0%",
  "interest_rate_percentage": "12.0%",
  "monthly_payment": "298.67"
}</code></pre>
        </div>
    </div>
    
    <div class="endpoint">
        <span class="method get">GET</span>
        <span class="url">/api/loans/</span>
        <div class="auth-required">Authentication: Required</div>
        <p>Get a list of the authenticated user's loans.</p>
        
        <div class="response">
            <pre><code>[
  {
    "id": 1,
    "item_type": {
      "id": 1,
      "name": "Gold Jewelry"
    },
    "loan_term": {
      "id": 2,
      "months": 3,
      "interest_rate": 12.0
    },
    "item_description": "18K gold chain necklace",
    "item_value": "1000.00",
    "loan_amount": "800.00",
    "interest_amount": "96.00",
    "total_repayment": "896.00",
    "status": "Active",
    "start_date": "2023-11-15",
    "end_date": "2024-02-15",
    "days_remaining": 92
  },
  // More loans...
]</code></pre>
        </div>
    </div>
    
    <h2 id="users">Users API</h2>
    
    <div class="endpoint">
        <span class="method post">POST</span>
        <span class="url">/api/users/register/</span>
        <div class="auth-not-required">Authentication: Not Required</div>
        <p>Register a new user.</p>
        
        <div class="params">
            <h4>Request Body:</h4>
            <pre><code>{
  "username": "john_doe",
  "email": "<EMAIL>",
  "password": "securepassword123",
  "password2": "securepassword123",
  "first_name": "John",
  "last_name": "Doe"
}</code></pre>
        </div>
        
        <div class="response">
            <pre><code>{
  "username": "john_doe",
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe"
}</code></pre>
        </div>
    </div>
    
    <div class="endpoint">
        <span class="method post">POST</span>
        <span class="url">/api/users/token/</span>
        <div class="auth-not-required">Authentication: Not Required</div>
        <p>Get JWT token for authentication.</p>
        
        <div class="params">
            <h4>Request Body:</h4>
            <pre><code>{
  "username": "john_doe",
  "password": "securepassword123"
}</code></pre>
        </div>
        
        <div class="response">
            <pre><code>{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}</code></pre>
        </div>
    </div>
    
    <div class="endpoint">
        <span class="method post">POST</span>
        <span class="url">/api/users/token/refresh/</span>
        <div class="auth-not-required">Authentication: Not Required</div>
        <p>Refresh JWT token.</p>
        
        <div class="params">
            <h4>Request Body:</h4>
            <pre><code>{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}</code></pre>
        </div>
        
        <div class="response">
            <pre><code>{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}</code></pre>
        </div>
    </div>
    
    <div class="endpoint">
        <span class="method get">GET</span>
        <span class="url">/api/users/profile/</span>
        <div class="auth-required">Authentication: Required</div>
        <p>Get the authenticated user's profile.</p>
        
        <div class="response">
            <pre><code>{
  "id": 1,
  "username": "john_doe",
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "profile": {
    "phone_number": "+27123456789",
    "address": "123 Main St",
    "city": "Cape Town",
    "province": "Western Cape",
    "postal_code": "8001",
    "profile_picture": "/media/profile_pictures/john_doe.jpg"
  }
}</code></pre>
        </div>
    </div>
    
    <div class="endpoint">
        <span class="method put">PUT</span>
        <span class="url">/api/users/profile/</span>
        <div class="auth-required">Authentication: Required</div>
        <p>Update the authenticated user's profile.</p>
        
        <div class="params">
            <h4>Request Body:</h4>
            <pre><code>{
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "profile": {
    "phone_number": "+27123456789",
    "address": "456 New St",
    "city": "Johannesburg",
    "province": "Gauteng",
    "postal_code": "2000"
  }
}</code></pre>
        </div>
        
        <div class="response">
            <pre><code>{
  "id": 1,
  "username": "john_doe",
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "profile": {
    "phone_number": "+27123456789",
    "address": "456 New St",
    "city": "Johannesburg",
    "province": "Gauteng",
    "postal_code": "2000",
    "profile_picture": "/media/profile_pictures/john_doe.jpg"
  }
}</code></pre>
        </div>
    </div>
    
    <div class="endpoint">
        <span class="method post">POST</span>
        <span class="url">/api/users/contact/</span>
        <div class="auth-not-required">Authentication: Not Required</div>
        <p>Submit a contact form message.</p>
        
        <div class="params">
            <h4>Request Body:</h4>
            <pre><code>{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+27123456789",
  "subject": "Inquiry about Gold Items",
  "message": "I have some gold items I'd like to pawn. What's your process?"
}</code></pre>
        </div>
        
        <div class="response">
            <pre><code>{
  "message": "Your message has been sent successfully. We will get back to you soon."
}</code></pre>
        </div>
    </div>
    
    <div class="endpoint">
        <span class="method post">POST</span>
        <span class="url">/api/users/newsletter/subscribe/</span>
        <div class="auth-not-required">Authentication: Not Required</div>
        <p>Subscribe to the newsletter.</p>
        
        <div class="params">
            <h4>Request Body:</h4>
            <pre><code>{
  "email": "<EMAIL>"
}</code></pre>
        </div>
        
        <div class="response">
            <pre><code>{
  "message": "You have been successfully subscribed to our newsletter."
}</code></pre>
        </div>
    </div>
    
    <footer style="margin-top: 50px; text-align: center; color: #6b7280; border-top: 1px solid #e5e7eb; padding-top: 20px;">
        <p>SMSMALI Pawnshop API Documentation</p>
        <p>&copy; 2023 SMSMALI. All rights reserved.</p>
    </footer>
</body>
</html>