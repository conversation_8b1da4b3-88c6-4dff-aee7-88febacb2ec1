# Quick Deployment Guide for SMSMali on PythonAnywhere

## 🚀 Quick Start (5 minutes)

### Step 1: Prepare for deployment
```bash
# Build the frontend
cd frontend
npm run build

# Go back to root
cd ..
```

### Step 2: Push to Git
```bash
git add .
git commit -m "Production ready deployment"
git push origin main
```

### Step 3: Deploy on PythonAnywhere
```bash
# Clone repository
cd /home/<USER>
git clone https://github.com/yourusername/smsmali.git smsmali
cd smsmali

# Create virtual environment
python3.10 -m venv venv
source venv/bin/activate

# Install requirements
pip install -r requirements_production.txt

# Set up environment
cp .env.example .env
nano .env  # Edit with your MySQL credentials

# Run Django setup
python manage_production.py migrate
python manage_production.py collectstatic --noinput
python manage_production.py createsuperuser
```

### Step 4: Configure Web App
1. Go to PythonAnywhere Web tab
2. Create new web app (Manual configuration, Python 3.10)
3. Set:
   - **Source code**: `/home/<USER>/smsmali`
   - **Working directory**: `/home/<USER>/smsmali`
   - **WSGI file**: Copy from `smsmali/wsgi_production.py`
   - **Virtualenv**: `/home/<USER>/smsmali/venv`

4. Static files:
   - **URL**: `/static/` → **Directory**: `/home/<USER>/smsmali/staticfiles/`
   - **URL**: `/media/` → **Directory**: `/home/<USER>/smsmali/media/`

5. Reload web app

### Step 5: Test
Visit `https://yourusername.pythonanywhere.com`

## 🔧 Environment Variables (.env)

```env
SECRET_KEY=your-secret-key-here
DEBUG=False
DB_NAME=yourusername$smsmali
DB_USER=yourusername
DB_PASSWORD=your-mysql-password
DB_HOST=yourusername.mysql.pythonanywhere-services.com
DB_PORT=3306
```

## 📁 Directory Structure on PythonAnywhere

```
/home/<USER>/smsmali/
├── frontend/           # React source code
├── static/
│   └── frontend/       # Built React app
├── staticfiles/        # Collected static files
├── media/             # User uploads
├── logs/              # Application logs
├── venv/              # Virtual environment
├── .env               # Environment variables
├── manage_production.py
└── smsmali/
    ├── settings_production.py
    └── wsgi_production.py
```

## 🛠 Troubleshooting

### Frontend not loading?
- Check if `npm run build` completed successfully
- Verify static files mapping in web app settings
- Run `python manage_production.py collectstatic`

### Database errors?
- Check MySQL credentials in `.env`
- Verify database name format: `username$databasename`
- Test connection: `python manage_production.py dbshell`

### Import errors?
- Activate virtual environment: `source venv/bin/activate`
- Check if all requirements installed: `pip list`
- Verify Python version in web app settings

## 🔄 Updates

```bash
# Pull latest changes
git pull origin main

# Update dependencies
pip install -r requirements_production.txt

# Run migrations
python manage_production.py migrate

# Collect static files
python manage_production.py collectstatic --noinput

# Restart web app from dashboard
```

---

For detailed documentation, see `PRODUCTION_DEPLOYMENT.md`