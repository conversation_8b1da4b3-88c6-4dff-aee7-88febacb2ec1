<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SMSMALI Pawnshop API</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            text-align: center;
        }
        .container {
            background-color: #f9fafb;
            border-radius: 8px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-top: 50px;
        }
        h1 {
            color: #3b82f6;
            margin-bottom: 20px;
        }
        .logo {
            font-size: 3em;
            font-weight: bold;
            color: #3b82f6;
            margin-bottom: 10px;
        }
        .tagline {
            font-size: 1.2em;
            color: #6b7280;
            margin-bottom: 30px;
        }
        .links {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 40px;
        }
        .link-button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        .link-button:hover {
            background-color: #2563eb;
        }
        .secondary-button {
            background-color: #f3f4f6;
            color: #4b5563;
            border: 1px solid #d1d5db;
        }
        .secondary-button:hover {
            background-color: #e5e7eb;
        }
        .api-endpoints {
            text-align: left;
            max-width: 600px;
            margin: 40px auto;
            background-color: #f3f4f6;
            padding: 20px;
            border-radius: 4px;
        }
        .api-endpoints h3 {
            color: #4b5563;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 10px;
            margin-top: 0;
        }
        .endpoint {
            margin-bottom: 10px;
            font-family: monospace;
        }
        .get { color: #10b981; }
        .post { color: #3b82f6; }
        footer {
            margin-top: 50px;
            color: #6b7280;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">SMSMALI</div>
        <div class="tagline">Pawnshop API Backend</div>
        
        <p>Welcome to the SMSMALI Pawnshop API. This backend provides RESTful API endpoints for the SMSMALI Pawnshop application.</p>
        
        <div class="api-endpoints">
            <h3>Main API Endpoints</h3>
            <div class="endpoint"><span class="get">GET</span> /api/products/ - List all products</div>
            <div class="endpoint"><span class="get">GET</span> /api/products/categories/ - List all categories</div>
            <div class="endpoint"><span class="get">GET</span> /api/loans/item-types/ - List all item types</div>
            <div class="endpoint"><span class="get">GET</span> /api/loans/loan-terms/ - List all loan terms</div>
            <div class="endpoint"><span class="post">POST</span> /api/users/register/ - Register a new user</div>
            <div class="endpoint"><span class="post">POST</span> /api/users/token/ - Get JWT token</div>
        </div>
        
        <div class="links">
            <a href="/api/docs/" class="link-button">API Documentation</a>
            <a href="/admin/" class="link-button">Admin Panel</a>
            <a href="http://localhost:5173/" class="link-button secondary-button">Frontend App</a>
        </div>
    </div>
    
    <footer>
        <p>&copy; 2023 SMSMALI Pawnshop. All rights reserved.</p>
    </footer>
</body>
</html>