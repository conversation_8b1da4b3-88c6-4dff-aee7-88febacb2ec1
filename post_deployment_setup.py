#!/usr/bin/env python3
"""
Post-Deployment Setup Script for SMSMali
========================================

This script handles post-deployment tasks like creating superuser,
loading initial data, and verifying the deployment.

Usage:
    python post_deployment_setup.py

Author: SMSMali Development Team
"""

import os
import sys
import subprocess
import django
from pathlib import Path

# Setup Django environment
sys.path.append(str(Path(__file__).resolve().parent))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smsmali.settings_production')

try:
    django.setup()
except Exception as e:
    print(f"❌ Error setting up Django: {e}")
    print("Make sure you're in the project directory and Django is installed")
    sys.exit(1)

class PostDeploymentSetup:
    def __init__(self):
        self.project_dir = Path(__file__).resolve().parent
        print("🔧 SMSMali Post-Deployment Setup")
        print("-" * 40)

    def run_command(self, command, description):
        """Execute a management command"""
        print(f"⚡ {description}...")
        try:
            result = subprocess.run(command, shell=True, check=True, 
                                  capture_output=True, text=True)
            if result.stdout:
                print(f"   ✅ {result.stdout.strip()}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"   ❌ Error: {e}")
            if e.stderr:
                print(f"   📝 Details: {e.stderr}")
            return False

    def create_superuser(self):
        """Create Django superuser"""
        print("\n👤 Creating superuser account...")
        
        try:
            from django.contrib.auth import get_user_model
            User = get_user_model()
            
            # Check if superuser already exists
            if User.objects.filter(is_superuser=True).exists():
                print("   ✅ Superuser already exists")
                return True
            
            # Create superuser
            username = input("   Enter superuser username (default: admin): ").strip() or "admin"
            email = input("   Enter superuser email: ").strip()
            
            if not email:
                print("   ❌ Email is required")
                return False
            
            user = User.objects.create_superuser(
                username=username,
                email=email,
                password="admin123"  # Default password - should be changed
            )
            
            print(f"   ✅ Superuser '{username}' created successfully")
            print("   ⚠️  Default password: admin123 (please change immediately)")
            return True
            
        except Exception as e:
            print(f"   ❌ Error creating superuser: {e}")
            return False

    def load_initial_data(self):
        """Load initial data for the application"""
        print("\n📊 Loading initial data...")
        
        # Check if initial data script exists
        initial_data_script = self.project_dir / "generate_initial_data.py"
        if initial_data_script.exists():
            return self.run_command(
                f"python {initial_data_script}",
                "Loading initial products and loan data"
            )
        else:
            print("   ⚠️  Initial data script not found, skipping...")
            return True

    def verify_deployment(self):
        """Verify that the deployment is working correctly"""
        print("\n🔍 Verifying deployment...")
        
        checks = [
            ("python manage.py check", "Running Django system checks"),
            ("python manage.py check --deploy", "Running deployment checks"),
        ]
        
        all_passed = True
        for command, description in checks:
            if not self.run_command(command, description):
                all_passed = False
        
        return all_passed

    def test_database_connection(self):
        """Test database connection and basic queries"""
        print("\n🗄️  Testing database connection...")
        
        try:
            from django.db import connection
            from django.contrib.auth import get_user_model
            
            # Test connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
            
            print("   ✅ Database connection successful")
            
            # Test basic queries
            User = get_user_model()
            user_count = User.objects.count()
            print(f"   ✅ Found {user_count} users in database")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Database connection failed: {e}")
            return False

    def display_admin_info(self):
        """Display admin panel information"""
        print("\n🔐 ADMIN PANEL ACCESS")
        print("=" * 25)
        print("📍 Admin URL: https://smsmali.pythonanywhere.com/admin/")
        print("👤 Username: admin (or your chosen username)")
        print("🔑 Password: admin123 (change immediately!)")
        print("\n⚠️  IMPORTANT: Change the default password after first login!")

    def display_api_info(self):
        """Display API endpoint information"""
        print("\n🔌 API ENDPOINTS")
        print("=" * 17)
        endpoints = [
            "Products API: /api/products/",
            "Loans API: /api/loans/",
            "Users API: /api/users/",
            "API Documentation: /api/docs/"
        ]
        
        for endpoint in endpoints:
            print(f"   📡 {endpoint}")

    def display_next_steps(self):
        """Display next steps for the user"""
        print("\n📋 NEXT STEPS")
        print("=" * 15)
        steps = [
            "Visit your website: https://smsmali.pythonanywhere.com",
            "Login to admin panel and change default password",
            "Review and customize initial data",
            "Test all functionality (products, loans, calculator)",
            "Configure any additional settings as needed",
            "Set up monitoring and backups",
            "Update contact information and business details"
        ]
        
        for i, step in enumerate(steps, 1):
            print(f"   {i}. {step}")

    def setup(self):
        """Run the complete post-deployment setup"""
        success = True
        
        # Test database connection first
        if not self.test_database_connection():
            print("❌ Database connection failed. Please check your configuration.")
            return False
        
        # Verify deployment
        if not self.verify_deployment():
            print("⚠️  Some deployment checks failed. Please review the issues.")
            success = False
        
        # Create superuser
        if not self.create_superuser():
            print("⚠️  Superuser creation failed.")
            success = False
        
        # Load initial data
        if not self.load_initial_data():
            print("⚠️  Initial data loading failed.")
            success = False
        
        # Display information
        self.display_admin_info()
        self.display_api_info()
        self.display_next_steps()
        
        if success:
            print("\n🎉 Post-deployment setup completed successfully!")
        else:
            print("\n⚠️  Setup completed with some issues. Please review the errors above.")
        
        return success

def main():
    setup = PostDeploymentSetup()
    setup.setup()

if __name__ == "__main__":
    main()
