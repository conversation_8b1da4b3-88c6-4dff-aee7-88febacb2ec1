import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Helmet } from 'react-helmet'
import { FaSearch, FaStar, FaStarHalfAlt, FaRegStar } from 'react-icons/fa'
import { Link } from 'react-router-dom'

const ProductsPage = () => {
  // Mock product data - in a real app, this would come from an API
  const allProducts = [
    {
      id: 1,
      name: "Vintage Gold Necklace",
      category: "jewelry",
      subcategory: "necklaces",
      price: 4999.99,
      rating: 4.8,
      image: "/images/products/gold-necklace.jpg",
      description: "Elegant 18K gold vintage necklace with intricate detailing.",
      condition: "Excellent",
      featured: true
    },
    {
      id: 2,
      name: "Diamond Engagement Ring",
      category: "jewelry",
      subcategory: "rings",
      price: 12500.00,
      rating: 5.0,
      image: "/images/products/diamond-ring.jpg",
      description: "1.5 carat diamond ring set in white gold.",
      condition: "Like New",
      featured: true
    },
    {
      id: 3,
      name: "Samsung Galaxy S21",
      category: "electronics",
      subcategory: "smartphones",
      price: 6999.99,
      rating: 4.5,
      image: "/images/products/samsung-s21.jpg",
      description: "Barely used Samsung Galaxy S21 with 128GB storage.",
      condition: "Very Good",
      featured: true
    },
    {
      id: 4,
      name: "MacBook Pro 2021",
      category: "electronics",
      subcategory: "laptops",
      price: 15999.99,
      rating: 4.7,
      image: "/images/products/macbook-pro.jpg",
      description: "Apple MacBook Pro with M1 chip, 16GB RAM, and 512GB SSD.",
      condition: "Excellent",
      featured: true
    },
    {
      id: 5,
      name: "Rolex Submariner",
      category: "watches",
      subcategory: "luxury",
      price: 89999.99,
      rating: 4.9,
      image: "/images/products/rolex-submariner.jpg",
      description: "Authentic Rolex Submariner with black dial and stainless steel bracelet.",
      condition: "Excellent",
      featured: true
    },
    {
      id: 6,
      name: "Canon EOS R5",
      category: "electronics",
      subcategory: "cameras",
      price: 29999.99,
      rating: 4.6,
      image: "/images/products/canon-r5.jpg",
      description: "Professional mirrorless camera with 45MP sensor and 8K video.",
      condition: "Very Good",
      featured: false
    },
    {
      id: 7,
      name: "Silver Bracelet",
      category: "jewelry",
      subcategory: "bracelets",
      price: 1299.99,
      rating: 4.3,
      image: "/images/products/silver-bracelet.jpg",
      description: "Sterling silver bracelet with charm details.",
      condition: "Good",
      featured: false
    },
    {
      id: 8,
      name: "iPad Pro 12.9\"",
      category: "electronics",
      subcategory: "tablets",
      price: 9999.99,
      rating: 4.7,
      image: "/images/products/ipad-pro.jpg",
      description: "Apple iPad Pro with M1 chip, 256GB storage, and Wi-Fi + Cellular.",
      condition: "Excellent",
      featured: false
    },
    {
      id: 9,
      name: "Pearl Earrings",
      category: "jewelry",
      subcategory: "earrings",
      price: 2499.99,
      rating: 4.5,
      image: "/images/products/pearl-earrings.jpg",
      description: "Elegant freshwater pearl earrings with 14K gold posts.",
      condition: "Like New",
      featured: false
    },
    {
      id: 10,
      name: "Omega Speedmaster",
      category: "watches",
      subcategory: "luxury",
      price: 45999.99,
      rating: 4.8,
      image: "/images/products/omega-speedmaster.jpg",
      description: "Iconic Omega Speedmaster Professional 'Moonwatch' with manual winding movement.",
      condition: "Very Good",
      featured: false
    },
    {
      id: 11,
      name: "Sony PlayStation 5",
      category: "electronics",
      subcategory: "gaming",
      price: 7999.99,
      rating: 4.9,
      image: "/images/products/ps5.jpg",
      description: "Sony PlayStation 5 console with one controller.",
      condition: "Excellent",
      featured: false
    },
    {
      id: 12,
      name: "Gold Bangle Set",
      category: "jewelry",
      subcategory: "bracelets",
      price: 8999.99,
      rating: 4.6,
      image: "/images/products/gold-bangles.jpg",
      description: "Set of three 22K gold bangles with traditional design.",
      condition: "Very Good",
      featured: false
    },
    {
      id: 13,
      name: "Seiko Automatic Watch",
      category: "watches",
      subcategory: "casual",
      price: 3499.99,
      rating: 4.4,
      image: "/images/products/seiko-watch.jpg",
      description: "Reliable Seiko automatic watch with stainless steel case and leather strap.",
      condition: "Good",
      featured: false
    },
    {
      id: 14,
      name: "Bose QuietComfort Earbuds",
      category: "electronics",
      subcategory: "audio",
      price: 2499.99,
      rating: 4.7,
      image: "/images/products/bose-earbuds.jpg",
      description: "Wireless noise-cancelling earbuds with excellent sound quality.",
      condition: "Like New",
      featured: false
    },
    {
      id: 15,
      name: "Diamond Tennis Bracelet",
      category: "jewelry",
      subcategory: "bracelets",
      price: 19999.99,
      rating: 4.9,
      image: "/images/products/tennis-bracelet.jpg",
      description: "Stunning diamond tennis bracelet with 3 carats of diamonds set in white gold.",
      condition: "Excellent",
      featured: false
    },
    {
      id: 16,
      name: "Tag Heuer Carrera",
      category: "watches",
      subcategory: "luxury",
      price: 29999.99,
      rating: 4.6,
      image: "/images/products/tag-heuer.jpg",
      description: "Elegant Tag Heuer Carrera chronograph with automatic movement.",
      condition: "Very Good",
      featured: false
    }
  ]

  const [products, setProducts] = useState([])
  const [filteredProducts, setFilteredProducts] = useState([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [priceRange, setPriceRange] = useState([0, 100000])
  const [sortOption, setSortOption] = useState('featured')
  const [currentPage, setCurrentPage] = useState(1)
  const productsPerPage = 8

  // Categories for filter
  const categories = [
    { id: 'all', name: 'All Categories' },
    { id: 'jewelry', name: 'Jewelry' },
    { id: 'watches', name: 'Watches' },
    { id: 'electronics', name: 'Electronics' }
  ]

  // Initialize products
  useEffect(() => {
    // Simulate API fetch delay
    setTimeout(() => {
      setProducts(allProducts)
      setFilteredProducts(allProducts)
    }, 500)
  }, [])

  // Filter products based on search, category, and price range
  useEffect(() => {
    let result = [...products]
    
    // Filter by search term
    if (searchTerm) {
      result = result.filter(product => 
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }
    
    // Filter by category
    if (selectedCategory !== 'all') {
      result = result.filter(product => product.category === selectedCategory)
    }
    
    // Filter by price range
    result = result.filter(product => 
      product.price >= priceRange[0] && product.price <= priceRange[1]
    )
    
    // Sort products
    switch (sortOption) {
      case 'price-low':
        result.sort((a, b) => a.price - b.price)
        break
      case 'price-high':
        result.sort((a, b) => b.price - a.price)
        break
      case 'rating':
        result.sort((a, b) => b.rating - a.rating)
        break
      case 'featured':
      default:
        result.sort((a, b) => (b.featured ? 1 : 0) - (a.featured ? 1 : 0))
        break
    }
    
    setFilteredProducts(result)
    setCurrentPage(1) // Reset to first page when filters change
  }, [searchTerm, selectedCategory, priceRange, sortOption, products])

  // Get current products for pagination
  const indexOfLastProduct = currentPage * productsPerPage
  const indexOfFirstProduct = indexOfLastProduct - productsPerPage
  const currentProducts = filteredProducts.slice(indexOfFirstProduct, indexOfLastProduct)
  const totalPages = Math.ceil(filteredProducts.length / productsPerPage)

  // Change page
  const paginate = (pageNumber) => setCurrentPage(pageNumber)

  // Handle search input
  const handleSearch = (e) => {
    setSearchTerm(e.target.value)
  }

  // Handle category selection
  const handleCategoryChange = (category) => {
    setSelectedCategory(category)
  }

  // Handle price range change
  const handlePriceChange = (e) => {
    const value = parseInt(e.target.value)
    setPriceRange([0, value])
  }

  // Handle sort option change
  const handleSortChange = (e) => {
    setSortOption(e.target.value)
  }

  // Render star ratings
  const renderRating = (rating) => {
    const stars = []
    const fullStars = Math.floor(rating)
    const hasHalfStar = rating % 1 >= 0.5
    
    for (let i = 1; i <= 5; i++) {
      if (i <= fullStars) {
        stars.push(<FaStar key={i} className="text-yellow-500" />)
      } else if (i === fullStars + 1 && hasHalfStar) {
        stars.push(<FaStarHalfAlt key={i} className="text-yellow-500" />)
      } else {
        stars.push(<FaRegStar key={i} className="text-yellow-500" />)
      }
    }
    
    return (
      <div className="flex items-center">
        {stars}
        <span className="ml-1 text-sm text-gray-600">({rating})</span>
      </div>
    )
  }

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6 }
    }
  }

  const staggerContainer = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  return (
    <>
      <Helmet>
        <title>Products - SMSMali Pawnshop</title>
        <meta name="description" content="Browse our collection of quality pre-owned jewelry, watches, electronics, and more at SMSMali Pawnshop in Midrand." />
      </Helmet>

      {/* Hero Section */}
      <section className="relative bg-gradient-to-b from-primary/10 to-base-100 py-20 md:py-28">
        <div className="container mx-auto px-4">
          <motion.div 
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="max-w-4xl mx-auto text-center"
          >
            <motion.h1 
              variants={fadeIn}
              className="text-4xl md:text-5xl font-bold mb-6 text-gradient-primary"
            >
              Our Products
            </motion.h1>
            <motion.p 
              variants={fadeIn}
              className="text-lg md:text-xl mb-8 text-base-content/80"
            >
              Browse our collection of quality pre-owned items at unbeatable prices
            </motion.p>
          </motion.div>
        </div>
      </section>

      {/* Products Section */}
      <section className="py-16 bg-base-100">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Filters Sidebar */}
            <div className="lg:w-1/4">
              <div className="bg-base-200 p-6 rounded-lg shadow-md sticky top-24">
                <h2 className="text-xl font-bold mb-6">Filters</h2>
                
                {/* Search */}
                <div className="mb-6">
                  <label className="block text-sm font-medium mb-2">Search</label>
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Search products..."
                      value={searchTerm}
                      onChange={handleSearch}
                      className="input input-bordered w-full pr-10"
                    />
                    <FaSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                </div>
                
                {/* Categories */}
                <div className="mb-6">
                  <label className="block text-sm font-medium mb-2">Categories</label>
                  <div className="space-y-2">
                    {categories.map(category => (
                      <div key={category.id} className="form-control">
                        <label className="label cursor-pointer justify-start">
                          <input
                            type="radio"
                            name="category"
                            className="radio radio-sm radio-primary"
                            checked={selectedCategory === category.id}
                            onChange={() => handleCategoryChange(category.id)}
                          />
                          <span className="label-text ml-2">{category.name}</span>
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
                
                {/* Price Range */}
                <div className="mb-6">
                  <label className="block text-sm font-medium mb-2">
                    Price Range: R0 - R{priceRange[1].toLocaleString()}
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="100000"
                    step="1000"
                    value={priceRange[1]}
                    onChange={handlePriceChange}
                    className="range range-primary"
                  />
                  <div className="flex justify-between text-xs mt-1">
                    <span>R0</span>
                    <span>R25,000</span>
                    <span>R50,000</span>
                    <span>R100,000</span>
                  </div>
                </div>
                
                {/* Sort By */}
                <div>
                  <label className="block text-sm font-medium mb-2">Sort By</label>
                  <select
                    value={sortOption}
                    onChange={handleSortChange}
                    className="select select-bordered w-full"
                  >
                    <option value="featured">Featured</option>
                    <option value="price-low">Price: Low to High</option>
                    <option value="price-high">Price: High to Low</option>
                    <option value="rating">Highest Rated</option>
                  </select>
                </div>
              </div>
            </div>
            
            {/* Products Grid */}
            <div className="lg:w-3/4">
              {filteredProducts.length === 0 ? (
                <div className="text-center py-12">
                  <h3 className="text-xl font-medium mb-4">No products found</h3>
                  <p className="text-base-content/70 mb-6">Try adjusting your search or filter criteria</p>
                  <button 
                    onClick={() => {
                      setSearchTerm('')
                      setSelectedCategory('all')
                      setPriceRange([0, 100000])
                      setSortOption('featured')
                    }}
                    className="btn btn-primary"
                  >
                    Reset Filters
                  </button>
                </div>
              ) : (
                <>
                  <div className="flex justify-between items-center mb-6">
                    <p className="text-base-content/70">
                      Showing {indexOfFirstProduct + 1}-{Math.min(indexOfLastProduct, filteredProducts.length)} of {filteredProducts.length} products
                    </p>
                  </div>
                  
                  <motion.div 
                    initial="hidden"
                    animate="visible"
                    variants={staggerContainer}
                    className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
                  >
                    {currentProducts.map(product => (
                      <motion.div 
                        key={product.id}
                        variants={fadeIn}
                        className="bg-base-200 rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-shadow duration-300 flex flex-col h-full"
                      >
                        <div className="relative h-48 overflow-hidden">
                          <img 
                            src={product.image} 
                            alt={product.name}
                            className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                            onError={(e) => {
                              e.target.onerror = null;
                              e.target.src = `https://placehold.co/400x300/primary/white?text=${product.name.replace(/ /g, '+')}`;
                            }}
                          />
                          {product.featured && (
                            <span className="absolute top-2 right-2 bg-primary text-primary-content text-xs font-bold px-2 py-1 rounded">
                              Featured
                            </span>
                          )}
                        </div>
                        
                        <div className="p-4 flex-grow flex flex-col">
                          <div className="mb-2">
                            {renderRating(product.rating)}
                          </div>
                          
                          <h3 className="text-lg font-bold mb-1 line-clamp-1">{product.name}</h3>
                          
                          <p className="text-base-content/70 text-sm mb-3 line-clamp-2">
                            {product.description}
                          </p>
                          
                          <div className="mt-auto">
                            <div className="flex justify-between items-center mb-3">
                              <span className="text-primary font-bold text-lg">R{product.price.toLocaleString()}</span>
                              <span className="text-xs bg-base-300 px-2 py-1 rounded">{product.condition}</span>
                            </div>
                            
                            <Link 
                              to={`/products/${product.id}`}
                              className="btn btn-primary btn-sm w-full"
                            >
                              View Details
                            </Link>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </motion.div>
                  
                  {/* Pagination */}
                  {totalPages > 1 && (
                    <div className="flex justify-center mt-12">
                      <div className="join">
                        <button 
                          className="join-item btn"
                          onClick={() => paginate(Math.max(1, currentPage - 1))}
                          disabled={currentPage === 1}
                        >
                          «
                        </button>
                        
                        {[...Array(totalPages).keys()].map(number => (
                          <button
                            key={number + 1}
                            onClick={() => paginate(number + 1)}
                            className={`join-item btn ${currentPage === number + 1 ? 'btn-active' : ''}`}
                          >
                            {number + 1}
                          </button>
                        ))}
                        
                        <button 
                          className="join-item btn"
                          onClick={() => paginate(Math.min(totalPages, currentPage + 1))}
                          disabled={currentPage === totalPages}
                        >
                          »
                        </button>
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary text-primary-content">
        <div className="container mx-auto px-4 text-center">
          <motion.div 
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="max-w-3xl mx-auto"
          >
            <motion.h2 
              variants={fadeIn}
              className="text-3xl font-bold mb-6"
            >
              Don't See What You're Looking For?
            </motion.h2>
            <motion.p 
              variants={fadeIn}
              className="text-lg mb-8"
            >
              Our inventory changes daily. Contact us to inquire about specific items or visit our store to see our latest arrivals.
            </motion.p>
            <motion.div variants={fadeIn}>
              <a href="/contact" className="btn btn-secondary btn-lg mr-4">Contact Us</a>
              <a href="/services" className="btn btn-outline btn-lg border-white text-white hover:bg-white hover:text-primary">Our Services</a>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </>
  )
}

export default ProductsPage