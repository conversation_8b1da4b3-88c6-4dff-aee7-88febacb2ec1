import { useState } from 'react'
import { Link, NavLink } from 'react-router-dom'
import { FaBars, FaTimes, FaPhone } from 'react-icons/fa'

const Navbar = ({ scrolled }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  const closeMenu = () => {
    setIsMenuOpen(false)
  }

  const navLinks = [
    { name: 'Home', path: '/' },
    { name: 'About', path: '/about' },
    { name: 'Services', path: '/services' },
    { name: 'Products', path: '/products' },
    { name: 'Loan Calculator', path: '/loan-calculator' },
    { name: 'Contact', path: '/contact' },
  ]

  return (
    <header className={`fixed top-0 left-0 w-full z-50 transition-all duration-300 ${scrolled ? 'bg-white shadow-md py-2' : 'bg-transparent py-4'}`}>
      <div className="container mx-auto px-4">
        <nav className="flex justify-between items-center">
          {/* Logo */}
          <Link to="/" className="flex items-center" onClick={closeMenu}>
            <span className="text-2xl font-bold text-primary">SMS<span className="text-secondary">Mali</span></span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8">
            <ul className="flex space-x-6">
              {navLinks.map((link) => (
                <li key={link.name}>
                  <NavLink 
                    to={link.path} 
                    className={({ isActive }) => 
                      `font-medium hover:text-primary transition-colors ${isActive ? 'text-primary' : scrolled ? 'text-neutral' : 'text-white'}`
                    }
                  >
                    {link.name}
                  </NavLink>
                </li>
              ))}
            </ul>
            <a 
              href="tel:+27123456789" 
              className="btn btn-primary btn-sm text-white flex items-center gap-2"
            >
              <FaPhone /> Call Us
            </a>
          </div>

          {/* Mobile Menu Button */}
          <button 
            className="lg:hidden text-2xl focus:outline-none z-20" 
            onClick={toggleMenu}
            aria-label="Toggle menu"
          >
            {isMenuOpen ? 
              <FaTimes className={scrolled ? 'text-neutral' : 'text-white'} /> : 
              <FaBars className={scrolled ? 'text-neutral' : 'text-white'} />}
          </button>

          {/* Mobile Navigation */}
          <div className={`fixed inset-0 bg-white z-10 transform ${isMenuOpen ? 'translate-x-0' : 'translate-x-full'} transition-transform duration-300 lg:hidden`}>
            <div className="flex flex-col h-full justify-center items-center">
              <ul className="flex flex-col space-y-6 text-center">
                {navLinks.map((link) => (
                  <li key={link.name}>
                    <NavLink 
                      to={link.path} 
                      className={({ isActive }) => 
                        `text-xl font-medium hover:text-primary transition-colors ${isActive ? 'text-primary' : 'text-neutral'}`
                      }
                      onClick={closeMenu}
                    >
                      {link.name}
                    </NavLink>
                  </li>
                ))}
                <li className="pt-6">
                  <a 
                    href="tel:+27123456789" 
                    className="btn btn-primary text-white flex items-center gap-2 mx-auto"
                    onClick={closeMenu}
                  >
                    <FaPhone /> Call Us
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </nav>
      </div>
    </header>
  )
}

export default Navbar