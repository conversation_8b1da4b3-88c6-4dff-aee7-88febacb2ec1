#!/usr/bin/env python3
"""
MCP Deployment Client for SMSMali
=================================

This client script provides a command-line interface to interact with
the SMSMali deployment MCP server.

Usage:
    python mcp_deployment_client.py deploy
    python mcp_deployment_client.py status
    python mcp_deployment_client.py config
    python mcp_deployment_client.py django-commands
    python mcp_deployment_client.py git-status

Author: SMSMali Development Team
"""

import asyncio
import json
import sys
import argparse
from typing import Any, Dict, List

try:
    from mcp.client.session import ClientSession
    from mcp.client.stdio import stdio_client
except ImportError:
    print("❌ MCP not installed. Install with: pip install mcp")
    sys.exit(1)

class SMSMaliDeploymentClient:
    def __init__(self):
        self.session = None
    
    async def connect(self):
        """Connect to the MCP server"""
        try:
            # Start the server process
            server_process = await asyncio.create_subprocess_exec(
                "python", "mcp_deployment_server.py",
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            # Create client session
            read_stream, write_stream = stdio_client(server_process)
            self.session = ClientSession(read_stream, write_stream)
            
            # Initialize the session
            await self.session.initialize()
            
            print("✅ Connected to SMSMali deployment server")
            return True
            
        except Exception as e:
            print(f"❌ Failed to connect to server: {e}")
            return False
    
    async def list_tools(self):
        """List available tools"""
        if not self.session:
            print("❌ Not connected to server")
            return
        
        try:
            tools = await self.session.list_tools()
            print("🔧 Available Tools:")
            print("=" * 20)
            for tool in tools.tools:
                print(f"📋 {tool.name}")
                print(f"   Description: {tool.description}")
                print()
        except Exception as e:
            print(f"❌ Error listing tools: {e}")
    
    async def deploy_from_git(self, **kwargs):
        """Deploy from Git repository"""
        if not self.session:
            print("❌ Not connected to server")
            return
        
        try:
            print("🚀 Starting deployment from Git...")
            result = await self.session.call_tool("deploy_from_git", kwargs)
            
            for content in result.content:
                if hasattr(content, 'text'):
                    print(content.text)
                    
        except Exception as e:
            print(f"❌ Deployment failed: {e}")
    
    async def check_status(self, directory="/home/<USER>/SMSMali"):
        """Check deployment status"""
        if not self.session:
            print("❌ Not connected to server")
            return
        
        try:
            print("🔍 Checking deployment status...")
            result = await self.session.call_tool("check_deployment_status", {"directory": directory})
            
            for content in result.content:
                if hasattr(content, 'text'):
                    print(content.text)
                    
        except Exception as e:
            print(f"❌ Status check failed: {e}")
    
    async def get_webapp_config(self, **kwargs):
        """Get web app configuration"""
        if not self.session:
            print("❌ Not connected to server")
            return
        
        try:
            print("⚙️ Generating web app configuration...")
            result = await self.session.call_tool("update_webapp_config", kwargs)
            
            for content in result.content:
                if hasattr(content, 'text'):
                    print(content.text)
                    
        except Exception as e:
            print(f"❌ Config generation failed: {e}")
    
    async def run_django_commands(self, commands=None, directory="/home/<USER>/SMSMali"):
        """Run Django management commands"""
        if not self.session:
            print("❌ Not connected to server")
            return
        
        try:
            args = {"directory": directory}
            if commands:
                args["commands"] = commands
            
            print("🐍 Running Django commands...")
            result = await self.session.call_tool("run_django_commands", args)
            
            for content in result.content:
                if hasattr(content, 'text'):
                    print(content.text)
                    
        except Exception as e:
            print(f"❌ Django commands failed: {e}")
    
    async def check_git_status(self, directory="/home/<USER>/SMSMali"):
        """Check Git status"""
        if not self.session:
            print("❌ Not connected to server")
            return
        
        try:
            print("📋 Checking Git status...")
            result = await self.session.call_tool("check_git_status", {"directory": directory})
            
            for content in result.content:
                if hasattr(content, 'text'):
                    print(content.text)
                    
        except Exception as e:
            print(f"❌ Git status check failed: {e}")
    
    async def close(self):
        """Close the connection"""
        if self.session:
            await self.session.close()
            print("👋 Disconnected from server")

async def main():
    parser = argparse.ArgumentParser(description='SMSMali Deployment Client')
    parser.add_argument('action', choices=[
        'deploy', 'status', 'config', 'django-commands', 'git-status', 'list-tools'
    ], help='Action to perform')
    
    # Deployment options
    parser.add_argument('--git-url', default='https://github.com/CooperSystems-commits/SMSMali.git',
                       help='Git repository URL')
    parser.add_argument('--target-dir', default='/home/<USER>/SMSMali',
                       help='Target deployment directory')
    parser.add_argument('--production', action='store_true', default=True,
                       help='Deploy in production mode')
    parser.add_argument('--force-clean', action='store_true',
                       help='Force clean deployment')
    
    # Django commands
    parser.add_argument('--commands', nargs='+',
                       help='Django commands to run')
    
    # Directory options
    parser.add_argument('--directory', default='/home/<USER>/SMSMali',
                       help='Project directory')
    
    args = parser.parse_args()
    
    client = SMSMaliDeploymentClient()
    
    try:
        # Connect to server
        if not await client.connect():
            return 1
        
        # Execute requested action
        if args.action == 'deploy':
            await client.deploy_from_git(
                git_url=args.git_url,
                target_directory=args.target_dir,
                production=args.production,
                force_clean=args.force_clean
            )
        
        elif args.action == 'status':
            await client.check_status(args.directory)
        
        elif args.action == 'config':
            await client.get_webapp_config(
                project_directory=args.directory,
                domain='smsmali.pythonanywhere.com'
            )
        
        elif args.action == 'django-commands':
            await client.run_django_commands(
                commands=args.commands,
                directory=args.directory
            )
        
        elif args.action == 'git-status':
            await client.check_git_status(args.directory)
        
        elif args.action == 'list-tools':
            await client.list_tools()
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⏹️ Interrupted by user")
        return 1
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1
    finally:
        await client.close()

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
