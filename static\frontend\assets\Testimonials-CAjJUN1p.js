import{j as e}from"./three-DVx3d6Kc.js";import{r as n}from"./router-By-Xbj7l.js";import{g as d,e as u}from"./index-DwaqOzzB.js";import{m as l}from"./ui-C4lKJJfU.js";import"./vendor-DWLLDKvm.js";const t=[{id:1,name:"<PERSON>",role:"Regular Customer",image:"https://randomuser.me/api/portraits/women/1.jpg",rating:5,text:"SMSMali offered me the best rate for my gold jewelry when I needed quick cash. The process was smooth, transparent, and I was treated with respect. Highly recommended!"},{id:2,name:"<PERSON>",role:"Business Owner",image:"https://randomuser.me/api/portraits/men/2.jpg",rating:5,text:"I purchased a pre-owned laptop from SMSMali at an incredible price. It works perfectly and came with a warranty. The staff was knowledgeable and helped me find exactly what I needed."},{id:3,name:"<PERSON><PERSON><PERSON>",role:"Regular Customer",image:"https://randomuser.me/api/portraits/men/3.jpg",rating:4,text:"When I needed a loan quickly, SMSMali was there for me. The interest rates were fair, and the terms were clearly explained. I have used their services multiple times now."},{id:4,name:"Lerato Khumalo",role:"First-time Customer",image:"https://randomuser.me/api/portraits/women/4.jpg",rating:5,text:"I was nervous about using a pawnshop, but SMSMali made me feel comfortable. They gave me a fair appraisal for my watch and the loan process was quick and easy."},{id:5,name:"David Smith",role:"Collector",image:"https://randomuser.me/api/portraits/men/5.jpg",rating:5,text:"I found a rare vintage watch at SMSMali that I'd been searching for. The condition was excellent and the price was fair. The staff's knowledge about collectibles is impressive."}],v=()=>{const[s,o]=n.useState(0),[x,r]=n.useState([]);n.useEffect(()=>{const a=()=>{const i=window.innerWidth;i>=1024?r(t.slice(0,3)):i>=768?r(t.slice(0,2)):r([t[s]])};return a(),window.addEventListener("resize",a),()=>window.removeEventListener("resize",a)},[s]);const h=()=>{o(a=>(a+1)%t.length)},p=()=>{o(a=>(a-1+t.length)%t.length)},m=a=>Array.from({length:5}).map((i,c)=>e.jsx(u,{className:c<a?"text-yellow-500":"text-gray-300"},c));return e.jsx("section",{className:"py-20 bg-base-100",children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsxs(l.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0,margin:"-100px"},transition:{duration:.5},className:"text-center mb-16",children:[e.jsxs("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:["What Our ",e.jsx("span",{className:"text-primary",children:"Clients Say"})]}),e.jsx("p",{className:"text-neutral max-w-2xl mx-auto",children:"Don\\'t just take our word for it. Here\\'s what our satisfied customers have to say about their experience with SMSMali."})]}),e.jsx("div",{className:"block md:hidden",children:e.jsxs("div",{className:"relative",children:[e.jsx(l.div,{initial:{opacity:0,x:50},animate:{opacity:1,x:0},exit:{opacity:0,x:-50},transition:{duration:.5},className:"card bg-base-100 shadow-lg p-6",children:e.jsxs("div",{className:"flex flex-col items-center text-center",children:[e.jsx("div",{className:"avatar mb-4",children:e.jsx("div",{className:"w-20 h-20 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2",children:e.jsx("img",{src:t[s].image,alt:t[s].name})})}),e.jsx(d,{className:"text-3xl text-primary opacity-20 mb-4"}),e.jsx("p",{className:"text-neutral mb-6",children:t[s].text}),e.jsx("div",{className:"flex mb-2",children:m(t[s].rating)}),e.jsx("h3",{className:"font-bold",children:t[s].name}),e.jsx("p",{className:"text-sm text-neutral",children:t[s].role})]})},s),e.jsxs("div",{className:"flex justify-center mt-6 space-x-4",children:[e.jsx("button",{onClick:p,className:"btn btn-circle btn-sm btn-outline","aria-label":"Previous testimonial",children:"❮"}),e.jsx("button",{onClick:h,className:"btn btn-circle btn-sm btn-outline","aria-label":"Next testimonial",children:"❯"})]})]})}),e.jsx("div",{className:"hidden md:grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:x.map((a,i)=>e.jsx(l.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0,margin:"-100px"},transition:{duration:.5,delay:i*.1},className:"card bg-base-100 shadow-lg p-6 hover:shadow-xl transition-shadow duration-300",children:e.jsxs("div",{className:"flex flex-col items-center text-center",children:[e.jsx("div",{className:"avatar mb-4",children:e.jsx("div",{className:"w-20 h-20 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2",children:e.jsx("img",{src:a.image,alt:a.name})})}),e.jsx(d,{className:"text-3xl text-primary opacity-20 mb-4"}),e.jsx("p",{className:"text-neutral mb-6",children:a.text}),e.jsx("div",{className:"flex mb-2",children:m(a.rating)}),e.jsx("h3",{className:"font-bold",children:a.name}),e.jsx("p",{className:"text-sm text-neutral",children:a.role})]})},a.id))})]})})};export{v as default};
