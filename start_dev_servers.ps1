#!/usr/bin/env pwsh

# Change to project directory
Set-Location "c:/Users/<USER>/Documents/augment-projects/smsmali"

# Activate virtual environment
& .\django_venv\Scripts\Activate.ps1

Write-Host "Starting Django development server..." -ForegroundColor Green

# Start Django server in background
$djangoJob = Start-Job -ScriptBlock {
    Set-Location "c:/Users/<USER>/Documents/augment-projects/smsmali"
    & .\django_venv\Scripts\Activate.ps1
    python manage.py runserver
}

Write-Host "Django server starting..." -ForegroundColor Green

# Wait a moment for Django to start
Start-Sleep -Seconds 3

Write-Host "Starting React development server..." -ForegroundColor Green

# Start React server in background
$reactJob = Start-Job -ScriptBlock {
    Set-Location "c:/Users/<USER>/Documents/augment-projects/smsmali/frontend"
    npm run dev
}

Write-Host "React server starting..." -ForegroundColor Green

# Wait a moment for React to start
Start-Sleep -Seconds 5

Write-Host ""
Write-Host "Development servers are running!" -ForegroundColor Yellow
Write-Host "Django API: http://127.0.0.1:8000/" -ForegroundColor Cyan
Write-Host "React Frontend: http://localhost:5173/" -ForegroundColor Cyan
Write-Host ""
Write-Host "Press Ctrl+C to stop the servers..." -ForegroundColor Red

# Keep script running and show output
try {
    while ($djangoJob.State -eq "Running" -or $reactJob.State -eq "Running") {
        # Show Django output
        $djangoOutput = Receive-Job -Job $djangoJob -Keep
        if ($djangoOutput) {
            Write-Host "Django: $djangoOutput" -ForegroundColor Blue
        }
        
        # Show React output
        $reactOutput = Receive-Job -Job $reactJob -Keep
        if ($reactOutput) {
            Write-Host "React: $reactOutput" -ForegroundColor Magenta
        }
        
        Start-Sleep -Seconds 2
    }
} catch {
    Write-Host "Stopping servers..." -ForegroundColor Red
} finally {
    # Clean up jobs
    Stop-Job -Job $djangoJob -ErrorAction SilentlyContinue
    Stop-Job -Job $reactJob -ErrorAction SilentlyContinue
    Remove-Job -Job $djangoJob -ErrorAction SilentlyContinue
    Remove-Job -Job $reactJob -ErrorAction SilentlyContinue
    Write-Host "Servers stopped." -ForegroundColor Green
}