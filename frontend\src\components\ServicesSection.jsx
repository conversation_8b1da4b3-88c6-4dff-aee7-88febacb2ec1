import { motion } from 'framer-motion'
import { FaMoneyBillWave, FaGem, FaShoppingBag, FaSearchDollar, FaTools } from 'react-icons/fa'
import { Link } from 'react-router-dom'

const services = [
  {
    id: 'loans',
    icon: <FaMoneyBillWave className="text-4xl text-secondary" />,
    title: 'Pawn Loans',
    description: 'Get instant cash loans on your valuables with competitive interest rates and flexible terms.',
  },
  {
    id: 'buying',
    icon: <FaGem className="text-4xl text-secondary" />,
    title: 'Buying Valuables',
    description: 'We buy gold, jewelry, electronics, and other valuable items at the best market prices.',
  },
  {
    id: 'selling',
    icon: <FaShoppingBag className="text-4xl text-secondary" />,
    title: 'Selling Items',
    description: 'Browse our collection of quality pre-owned items at affordable prices with warranty.',
  },
  {
    id: 'appraisal',
    icon: <FaSearchDollar className="text-4xl text-secondary" />,
    title: 'Item Appraisal',
    description: 'Get professional appraisal services for your valuable items from our experienced team.',
  },
  {
    id: 'jewelry',
    icon: <FaTools className="text-4xl text-secondary" />,
    title: 'Jewelry Cleaning',
    description: 'Professional jewelry cleaning and minor repair services to keep your items looking their best.',
  },
]

const ServicesSection = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  }
  
  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  }

  return (
    <section id="services" className="py-20 bg-base-100">
      <div className="container mx-auto px-4">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Our <span className="text-primary">Services</span>
          </h2>
          <p className="text-neutral max-w-2xl mx-auto">
            SMSMali offers a comprehensive range of pawnbroking services to meet your financial needs and help you find quality pre-owned items.
          </p>
        </motion.div>

        <motion.div 
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {services.map((service) => (
            <motion.div 
              key={service.id}
              variants={itemVariants}
              className="card bg-base-100 shadow-lg hover:shadow-xl card-hover"
            >
              <div className="card-body text-center">
                <div className="flex justify-center mb-4">
                  {service.icon}
                </div>
                <h3 className="card-title text-xl font-bold justify-center mb-2">{service.title}</h3>
                <p className="text-neutral">{service.description}</p>
              </div>
            </motion.div>
          ))}
        </motion.div>

        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="text-center mt-12"
        >
          <Link to="/services" className="btn btn-primary text-white px-8">
            View All Services
          </Link>
        </motion.div>
      </div>
    </section>
  )
}

export default ServicesSection