#!/usr/bin/env python
import os
import sys
import subprocess
import time

def run_command(command, description):
    print(f"\n{description}...")
    process = subprocess.run(command, shell=True)
    if process.returncode != 0:
        print(f"Error executing: {command}")
        sys.exit(1)
    return process

def main():
    # Check if virtual environment is activated
    if not os.environ.get('VIRTUAL_ENV'):
        print("Warning: Virtual environment not detected. It's recommended to activate it first.")
        response = input("Continue anyway? (y/n): ")
        if response.lower() != 'y':
            print("Exiting. Please activate the virtual environment and try again.")
            return
    
    print("=== SMSMALI Project Initialization ===")
    
    # Install Python dependencies
    run_command("pip install -r requirements.txt", "Installing Python dependencies")
    
    # Create necessary directories
    os.makedirs('media/products', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    
    # Run migrations
    run_command("python manage.py makemigrations", "Creating migrations")
    run_command("python manage.py migrate", "Applying migrations")
    
    # Generate initial data
    run_command("python generate_initial_data.py", "Generating initial data")
    
    # Collect static files
    run_command("python manage.py collectstatic --noinput", "Collecting static files")
    
    # Install frontend dependencies
    if os.path.exists('frontend'):
        os.chdir('frontend')
        run_command("npm install", "Installing frontend dependencies")
        os.chdir('..')
    
    print("\n=== Project initialization complete! ===")
    print("\nYou can now run the development servers with:")
    print("python run_dev_server.py")
    print("\nOr separately:")
    print("- Django: python manage.py runserver")
    print("- React: cd frontend && npm run dev")

if __name__ == "__main__":
    main()