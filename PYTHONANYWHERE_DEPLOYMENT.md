# SMSMali PythonAnywhere Deployment Guide

Complete guide for deploying SMSMali to PythonAnywhere with fixed dependencies.

## 🚀 Quick Deployment (Recommended)

### Step 1: Access PythonAnywhere Console
1. Login to your PythonAnywhere account
2. Go to **Console** tab
3. Open a **Bash console**

### Step 2: Clone/Update Repository
```bash
# Navigate to home directory
cd /home/<USER>

# Clone repository (first time)
<NAME_EMAIL>:CooperSystems-commits/SMSMali.git
cd SMSMali

# OR update existing repository
cd SMSMali
git pull origin master
```

### Step 3: Run Simple Deployment
```bash
# Run the simple deployment script
python3.10 deploy_simple.py --production
```

### Step 4: Generate Web App Configuration
```bash
# Run the advanced webapp configuration script
python3.10 setup_pythonanywhere_webapp.py --validate --generate-config
```

**That's it!** The scripts will handle everything automatically and provide you with:
- ✅ Complete deployment setup
- ✅ Auto-detected correct paths (/home/<USER>/SMSMali)
- ✅ Generated secret keys and configuration files
- ✅ Deployment readiness validation
- ✅ Step-by-step PythonAnywhere setup instructions

## 🔧 Manual Deployment (Alternative)

If you prefer manual control:

### Step 1: Setup Virtual Environment
```bash
cd /home/<USER>/SMSMali

# Create virtual environment
python3.10 -m venv venv

# Activate virtual environment
source venv/bin/activate

# Upgrade pip
pip install --upgrade pip
```

### Step 2: Install Dependencies
```bash
# Install core packages first
pip install "Django>=4.2,<5.0"
pip install "djangorestframework>=3.14"
pip install "django-cors-headers>=4.3"
pip install "django-filter>=23.0"
pip install "Pillow>=10.0"
pip install "python-dotenv>=1.0"
pip install "whitenoise>=6.6"

# Install remaining requirements
pip install -r requirements.txt
```

### Step 3: Django Setup
```bash
# Collect static files
python manage.py collectstatic --noinput --settings=smsmali.settings_production

# Run migrations
python manage.py migrate --settings=smsmali.settings_production

# Create superuser (optional)
python manage.py createsuperuser --settings=smsmali.settings_production
```

### Step 4: Generate Web App Configuration
```bash
# Run the advanced webapp configuration script
python setup_pythonanywhere_webapp.py --validate --generate-config

# This will:
# - Auto-detect your project directory (/home/<USER>/SMSMali)
# - Validate Django project structure
# - Generate secure secret keys
# - Create comprehensive configuration files
# - Provide deployment readiness check
```

## 🌐 PythonAnywhere Web App Configuration

### Step 1: Generate Configuration (Recommended)
```bash
# Run the advanced configuration script
python3.10 setup_pythonanywhere_webapp.py --validate --generate-config
```

This script will:
- ✅ **Auto-detect** your project directory (`/home/<USER>/SMSMali`)
- ✅ **Validate** Django project structure and configuration
- ✅ **Generate** secure secret keys automatically
- ✅ **Create** comprehensive configuration files
- ✅ **Provide** step-by-step setup instructions
- ✅ **Check** deployment readiness

### Step 2: Create Web App
1. Go to **Web** tab in PythonAnywhere
2. Click **Add a new web app**
3. Choose **Manual configuration**
4. Select **Python 3.10**

### Step 3: Configure Web App Settings (Use Generated Values)

#### Source Code
```
/home/<USER>/SMSMali
```

#### WSGI Configuration File
```
/home/<USER>/SMSMali/wsgi.py
```

#### Virtual Environment
```
/home/<USER>/SMSMali/venv
```

### Step 4: Static Files Mapping
Add these static file mappings:

| URL | Directory | Description |
|-----|-----------|-------------|
| `/static/` | `/home/<USER>/SMSMali/staticfiles` | Django static files (CSS, JS, images) |
| `/media/` | `/home/<USER>/SMSMali/media` | User uploaded files |

### Step 5: Environment Variables
Add these environment variables (use values from generated config):

| Name | Value | Description |
|------|-------|-------------|
| `DJANGO_ENV` | `production` | Set Django to production mode |
| `DJANGO_SECRET_KEY` | `[use generated key]` | Django secret key for security |
| `DJANGO_SETTINGS_MODULE` | `smsmali.settings_production` | Use production settings |

**The configuration script generates a secure secret key automatically!**

### Step 6: Reload Web App
Click the **Reload** button to apply all changes.

### Step 7: Verify Deployment
- ✅ **Main Site**: https://smsmali.pythonanywhere.com
- ✅ **Admin Panel**: https://smsmali.pythonanywhere.com/admin/
- ✅ **API Docs**: https://smsmali.pythonanywhere.com/api/docs/

## 🔍 Troubleshooting

### Common Issues

#### 1. Import Errors
```bash
# Check if packages are installed
pip list | grep django
pip list | grep pillow

# Reinstall if missing
pip install "Django>=4.2,<5.0"
pip install "Pillow>=10.0"
```

#### 2. Static Files Not Loading
```bash
# Recollect static files
python manage.py collectstatic --noinput --settings=smsmali.settings_production

# Check static files directory
ls -la /home/<USER>/SMSMali/staticfiles/
```

#### 3. Database Errors
```bash
# Run migrations
python manage.py migrate --settings=smsmali.settings_production

# Check database file
ls -la /home/<USER>/SMSMali/db.sqlite3
```

#### 4. Permission Errors
```bash
# Fix permissions
chmod -R 755 /home/<USER>/SMSMali/
chmod 644 /home/<USER>/SMSMali/db.sqlite3
```

### Debug Commands

#### Check Django Configuration
```bash
cd /home/<USER>/SMSMali
source venv/bin/activate
python manage.py check --settings=smsmali.settings_production
python manage.py check --deploy --settings=smsmali.settings_production
```

#### Test Django Shell
```bash
python manage.py shell --settings=smsmali.settings_production
```

#### View Error Logs
```bash
# Check error logs in PythonAnywhere Web tab
# Or check Django log file
tail -f /home/<USER>/SMSMali/django.log
```

## 📋 Deployment Checklist

### Before Deployment
- [ ] Code pushed to GitHub
- [ ] Frontend built and included in static files
- [ ] Requirements updated with latest versions
- [ ] Production settings configured

### During Deployment
- [ ] Repository cloned/updated on PythonAnywhere
- [ ] Virtual environment created
- [ ] Dependencies installed successfully
- [ ] Static files collected
- [ ] Database migrations applied
- [ ] WSGI file created

### After Deployment
- [ ] Web app configured in PythonAnywhere
- [ ] Static file mappings added
- [ ] Environment variables set
- [ ] Secret key generated and set
- [ ] Web app reloaded
- [ ] Site accessible at smsmali.pythonanywhere.com

## 🔄 Updates and Maintenance

### Updating Code
```bash
cd /home/<USER>/SMSMali
git pull origin master
source venv/bin/activate
pip install -r requirements.txt
python manage.py collectstatic --noinput --settings=smsmali.settings_production
python manage.py migrate --settings=smsmali.settings_production
```

### Updating Dependencies
```bash
source venv/bin/activate
pip install --upgrade "Django>=4.2,<5.0"
pip install --upgrade "Pillow>=10.0"
pip install --upgrade -r requirements.txt
```

## 🎯 Deployment Scripts Available

### 1. Simple Deployment (Recommended)
```bash
python3.10 deploy_simple.py --production
```
- Handles all dependency issues
- Creates production settings
- Runs Django commands
- Cross-platform compatible

### 2. Advanced Web App Configuration
```bash
python3.10 setup_pythonanywhere_webapp.py --validate --generate-config
```
- Auto-detects correct project paths (/home/<USER>/SMSMali)
- Validates Django project structure
- Generates secure secret keys
- Creates comprehensive configuration files
- Provides deployment readiness checks
- Includes security checklist

### 3. Fixed Deployment
```bash
python3.10 deploy_pythonanywhere_fixed.py --production
```
- Path-aware deployment
- Handles directory naming issues
- Comprehensive error handling

### 4. MCP Deployment (Advanced)
```bash
pip install -r requirements_mcp.txt
python3.10 mcp_deployment_client.py deploy --production

# After SSH setup and clone
cd /home/<USER>/SMSMali
pip3.10 install --user -r requirements_mcp.txt
python3.10 mcp_deployment_client.py deploy --production --force-clean --git-url **************:CooperSystems-commits/SMSMali.git
```
- Programmatic deployment interface
- Advanced monitoring and status checking
- API-based deployment tools

## � Advanced Configuration Script Features

The `setup_pythonanywhere_webapp.py` script provides comprehensive web app setup:

### **Auto-Detection & Validation:**
- ✅ Automatically detects project directory (`/home/<USER>/SMSMali`)
- ✅ Validates Django project structure (manage.py, settings, etc.)
- ✅ Checks virtual environment setup
- ✅ Tests Django configuration
- ✅ Provides deployment readiness score

### **Security & Configuration:**
- 🔐 Generates cryptographically secure secret keys
- ⚙️ Creates comprehensive configuration files
- 📋 Provides security checklist
- 🔍 Validates production settings

### **Usage Options:**
```bash
# Basic usage (auto-detect everything)
python3.10 setup_pythonanywhere_webapp.py

# Full validation and config generation
python3.10 setup_pythonanywhere_webapp.py --validate --generate-config

# Manual project directory override
python3.10 setup_pythonanywhere_webapp.py --project-dir /home/<USER>/SMSMali
```

### **Generated Files:**
- `pythonanywhere_webapp_config.json` - Complete configuration
- `pythonanywhere_config.json` - Basic configuration
- Secure secret keys for environment variables
- Step-by-step setup instructions

## �📞 Support

### If Deployment Fails:
1. Check the error messages carefully
2. Verify all paths are correct
3. Ensure virtual environment is activated
4. Check PythonAnywhere error logs
5. Try the simple deployment script

### Common Solutions:
- Use `python3.10` instead of `python`
- Ensure you're in the correct directory
- Check file permissions
- Verify environment variables are set

---

🎉 **Your SMSMali application should now be live at:**
**https://smsmali.pythonanywhere.com**

## 🔗 Quick Links
- **Admin Panel**: https://smsmali.pythonanywhere.com/admin/
- **API Documentation**: https://smsmali.pythonanywhere.com/api/docs/
- **PythonAnywhere Dashboard**: https://www.pythonanywhere.com/user/smsmali/
