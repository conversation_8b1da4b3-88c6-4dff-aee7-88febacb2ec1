# SMSMali PythonAnywhere Deployment Guide

Complete guide for deploying SMSMali to PythonAnywhere with fixed dependencies.

## 🚀 Quick Deployment (Recommended)

### Step 1: Access PythonAnywhere Console
1. Login to your PythonAnywhere account
2. Go to **Console** tab
3. Open a **Bash console**

### Step 2: Clone/Update Repository
```bash
# Navigate to home directory
cd /home/<USER>

# Clone repository (first time)
<NAME_EMAIL>:CooperSystems-commits/SMSMali.git
cd SMSMali

# OR update existing repository
cd SMSMali
git pull origin master
```

### Step 3: Run Simple Deployment
```bash
# Run the simple deployment script
python3.10 deploy_simple.py --production
```

**That's it!** The script will handle everything automatically.

## 🔧 Manual Deployment (Alternative)

If you prefer manual control:

### Step 1: Setup Virtual Environment
```bash
cd /home/<USER>/SMSMali

# Create virtual environment
python3.10 -m venv venv

# Activate virtual environment
source venv/bin/activate

# Upgrade pip
pip install --upgrade pip
```

### Step 2: Install Dependencies
```bash
# Install core packages first
pip install "Django>=4.2,<5.0"
pip install "djangorestframework>=3.14"
pip install "django-cors-headers>=4.3"
pip install "django-filter>=23.0"
pip install "Pillow>=10.0"
pip install "python-dotenv>=1.0"
pip install "whitenoise>=6.6"

# Install remaining requirements
pip install -r requirements.txt
```

### Step 3: Django Setup
```bash
# Collect static files
python manage.py collectstatic --noinput --settings=smsmali.settings_production

# Run migrations
python manage.py migrate --settings=smsmali.settings_production

# Create superuser (optional)
python manage.py createsuperuser --settings=smsmali.settings_production
```

## 🌐 PythonAnywhere Web App Configuration

### Step 1: Create Web App
1. Go to **Web** tab in PythonAnywhere
2. Click **Add a new web app**
3. Choose **Manual configuration**
4. Select **Python 3.10**

### Step 2: Configure Web App Settings

#### Source Code
```
/home/<USER>/SMSMali
```

#### WSGI Configuration File
```
/home/<USER>/SMSMali/wsgi.py
```

#### Virtual Environment
```
/home/<USER>/SMSMali/venv
```

### Step 3: Static Files Mapping
Add these static file mappings:

| URL | Directory |
|-----|-----------|
| `/static/` | `/home/<USER>/SMSMali/staticfiles` |
| `/media/` | `/home/<USER>/SMSMali/media` |

### Step 4: Environment Variables
Add these environment variables:

| Name | Value |
|------|-------|
| `DJANGO_ENV` | `production` |
| `DJANGO_SECRET_KEY` | `your-secret-key-here` |

**Generate a secret key:**
```bash
python -c "from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())"
```

### Step 5: Reload Web App
Click the **Reload** button to apply all changes.

## 🔍 Troubleshooting

### Common Issues

#### 1. Import Errors
```bash
# Check if packages are installed
pip list | grep django
pip list | grep pillow

# Reinstall if missing
pip install "Django>=4.2,<5.0"
pip install "Pillow>=10.0"
```

#### 2. Static Files Not Loading
```bash
# Recollect static files
python manage.py collectstatic --noinput --settings=smsmali.settings_production

# Check static files directory
ls -la /home/<USER>/SMSMali/staticfiles/
```

#### 3. Database Errors
```bash
# Run migrations
python manage.py migrate --settings=smsmali.settings_production

# Check database file
ls -la /home/<USER>/SMSMali/db.sqlite3
```

#### 4. Permission Errors
```bash
# Fix permissions
chmod -R 755 /home/<USER>/SMSMali/
chmod 644 /home/<USER>/SMSMali/db.sqlite3
```

### Debug Commands

#### Check Django Configuration
```bash
cd /home/<USER>/SMSMali
source venv/bin/activate
python manage.py check --settings=smsmali.settings_production
python manage.py check --deploy --settings=smsmali.settings_production
```

#### Test Django Shell
```bash
python manage.py shell --settings=smsmali.settings_production
```

#### View Error Logs
```bash
# Check error logs in PythonAnywhere Web tab
# Or check Django log file
tail -f /home/<USER>/SMSMali/django.log
```

## 📋 Deployment Checklist

### Before Deployment
- [ ] Code pushed to GitHub
- [ ] Frontend built and included in static files
- [ ] Requirements updated with latest versions
- [ ] Production settings configured

### During Deployment
- [ ] Repository cloned/updated on PythonAnywhere
- [ ] Virtual environment created
- [ ] Dependencies installed successfully
- [ ] Static files collected
- [ ] Database migrations applied
- [ ] WSGI file created

### After Deployment
- [ ] Web app configured in PythonAnywhere
- [ ] Static file mappings added
- [ ] Environment variables set
- [ ] Secret key generated and set
- [ ] Web app reloaded
- [ ] Site accessible at smsmali.pythonanywhere.com

## 🔄 Updates and Maintenance

### Updating Code
```bash
cd /home/<USER>/SMSMali
git pull origin master
source venv/bin/activate
pip install -r requirements.txt
python manage.py collectstatic --noinput --settings=smsmali.settings_production
python manage.py migrate --settings=smsmali.settings_production
```

### Updating Dependencies
```bash
source venv/bin/activate
pip install --upgrade "Django>=4.2,<5.0"
pip install --upgrade "Pillow>=10.0"
pip install --upgrade -r requirements.txt
```

## 🎯 Deployment Scripts Available

### 1. Simple Deployment (Recommended)
```bash
python3.10 deploy_simple.py --production
```
- Handles all dependency issues
- Creates production settings
- Runs Django commands
- Cross-platform compatible

### 2. Fixed Deployment
```bash
python3.10 deploy_pythonanywhere_fixed.py --production
```
- Path-aware deployment
- Handles directory naming issues
- Comprehensive error handling

### 3. MCP Deployment (Advanced)
```bash
pip install -r requirements_mcp.txt
python3.10 mcp_deployment_client.py deploy --production


```
- Programmatic deployment interface
- Advanced monitoring and status checking
- API-based deployment tools

## 📞 Support

### If Deployment Fails:
1. Check the error messages carefully
2. Verify all paths are correct
3. Ensure virtual environment is activated
4. Check PythonAnywhere error logs
5. Try the simple deployment script

### Common Solutions:
- Use `python3.10` instead of `python`
- Ensure you're in the correct directory
- Check file permissions
- Verify environment variables are set

---

🎉 **Your SMSMali application should now be live at:**
**https://smsmali.pythonanywhere.com**

## 🔗 Quick Links
- **Admin Panel**: https://smsmali.pythonanywhere.com/admin/
- **API Documentation**: https://smsmali.pythonanywhere.com/api/docs/
- **PythonAnywhere Dashboard**: https://www.pythonanywhere.com/user/smsmali/
