#!/usr/bin/env python3
"""
PythonAnywhere deployment script for SMSMali Pawnshop Application
This script should be run on PythonAnywhere after cloning the repository
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, cwd=None):
    """Run a shell command and return the result"""
    try:
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True,
            check=True
        )
        print(f"✅ {command}")
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running command: {command}")
        print(f"Error output: {e.stderr}")
        return None

def main():
    print("🚀 Starting PythonAnywhere deployment setup...")
    
    # Get the project root directory
    project_root = Path("/home/<USER>/smsmali")
    
    if not project_root.exists():
        print("❌ Project directory not found! Make sure you're running this from the correct location.")
        sys.exit(1)
    
    # Change to project directory
    os.chdir(project_root)
    
    # Create virtual environment
    print("📦 Creating virtual environment...")
    if not run_command("python3.10 -m venv venv"):
        print("❌ Failed to create virtual environment")
        sys.exit(1)
    
    # Activate virtual environment and install requirements
    print("📦 Installing Python requirements...")
    pip_cmd = f"{project_root}/venv/bin/pip"
    
    if not run_command(f"{pip_cmd} install --upgrade pip"):
        print("❌ Failed to upgrade pip")
        sys.exit(1)
    
    if not run_command(f"{pip_cmd} install -r requirements_production.txt"):
        print("❌ Failed to install requirements")
        sys.exit(1)
    
    print("✅ Python requirements installed")
    
    # Create necessary directories
    print("📁 Creating necessary directories...")
    directories = [
        "logs",
        "staticfiles",
        "media",
        "static/frontend"
    ]
    
    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    # Check if .env file exists
    env_file = project_root / ".env"
    if not env_file.exists():
        print("⚠️  .env file not found!")
        print("Please create .env file based on .env.example before continuing.")
        print("Run: cp .env.example .env")
        print("Then edit .env with your actual values.")
        return
    
    # Run Django management commands
    python_cmd = f"{project_root}/venv/bin/python"
    manage_cmd = f"{python_cmd} manage_production.py"
    
    print("🔧 Running Django migrations...")
    if not run_command(f"{manage_cmd} migrate"):
        print("❌ Failed to run migrations")
        sys.exit(1)
    
    print("📁 Collecting static files...")
    if not run_command(f"{manage_cmd} collectstatic --noinput"):
        print("❌ Failed to collect static files")
        sys.exit(1)
    
    print("🎉 PythonAnywhere deployment setup completed!")
    print("\nNext steps:")
    print("1. Configure your PythonAnywhere web app:")
    print("   - Source code: /home/<USER>/smsmali")
    print("   - Working directory: /home/<USER>/smsmali")
    print("   - WSGI file: /home/<USER>/smsmali/smsmali/wsgi_production.py")
    print("   - Python executable: /home/<USER>/smsmali/venv/bin/python")
    print("2. Set up static files mapping:")
    print("   - URL: /static/")
    print("   - Directory: /home/<USER>/smsmali/staticfiles/")
    print("3. Set up media files mapping:")
    print("   - URL: /media/")
    print("   - Directory: /home/<USER>/smsmali/media/")
    print("4. Create superuser: python manage_production.py createsuperuser")
    print("5. Test your application!")

if __name__ == "__main__":
    main()