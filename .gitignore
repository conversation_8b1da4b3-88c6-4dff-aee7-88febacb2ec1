# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
staticfiles/
logs/

# Virtual Environment
django_venv/
venv/
env/
ENV/

# Node.js / React
node_modules/
.npm
.pnp
.pnp.js
coverage/
.next/
out/
build/
.DS_Store
*.pem
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# OS specific
.DS_Store
Thumbs.db