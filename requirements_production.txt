# Production requirements for PythonAnywhere deployment
# SMSMali Pawnshop Application

# Django and Django REST Framework
Django>=4.2,<5.0
djangorestframework>=3.14
djangorestframework-simplejwt>=5.3
django-cors-headers>=4.3
django-filter>=23.0  # For filtering support

# Image Processing
Pillow>=10.0  # Latest stable version

# Database - Multiple options for flexibility
mysqlclient>=2.2  # For MySQL on PythonAnywhere
psycopg2-binary>=2.9  # For PostgreSQL (optional)

# Production deployment
whitenoise>=6.6
gunicorn>=21.0  # For production WSGI server

# Environment and configuration
python-dotenv>=1.0

# Utilities
python-slugify>=8.0

# API Documentation
drf-yasg>=1.21

# Security and performance
django-ratelimit>=4.1
django-extensions>=3.2

# Monitoring and logging
sentry-sdk[django]>=1.38

# Core dependencies
certifi>=2023.0
charset-normalizer>=3.0
idna>=3.0
requests>=2.31
urllib3>=2.0

# Additional useful packages for production
django-debug-toolbar>=4.0  # For development debugging
django-crispy-forms>=2.0  # For better forms
crispy-bootstrap5>=0.7  # Bootstrap 5 support
