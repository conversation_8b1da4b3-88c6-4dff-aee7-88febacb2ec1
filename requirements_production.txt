# Production requirements for PythonAnywhere deployment
# SMSMali Pawnshop Application

# Django and Django REST Framework
Django==4.2.7
djangorestframework==3.14.0
djangorestframework-simplejwt==5.3.0
django-cors-headers==4.3.0

# Image Processing
Pillow==10.1.0

# Database - MySQL for PythonAnywhere
mysqlclient==2.2.0

# Production deployment
whitenoise==6.6.0

# Environment and configuration
python-dotenv==1.0.0

# Utilities
python-slugify==8.0.1

# API Documentation
drf-yasg==1.21.7

# Core dependencies
certifi==2023.11.17
charset-normalizer==3.3.2
idna==3.6
requests==2.31.0
urllib3==2.1.0
