import { useState } from 'react'
import { motion } from 'framer-motion'
import { Helm<PERSON> } from 'react-helmet'
import { FaMapMarkerAlt, FaPhone, FaEnvelope, FaClock, FaWhatsapp } from 'react-icons/fa'

const ContactPage = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState(null)

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)
    setSubmitStatus(null)
    
    try {
      const response = await fetch('/api/users/contact/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      })
      
      if (response.ok) {
        setSubmitStatus('success')
        // Reset form after successful submission
        setFormData({
          name: '',
          email: '',
          phone: '',
          subject: '',
          message: ''
        })
        
        // Clear success message after 5 seconds
        setTimeout(() => {
          setSubmitStatus(null)
        }, 5000)
      } else {
        const errorData = await response.json()
        console.error('Contact form submission error:', errorData)
        setSubmitStatus('error')
        
        // Clear error message after 5 seconds
        setTimeout(() => {
          setSubmitStatus(null)
        }, 5000)
      }
    } catch (error) {
      console.error('Contact form submission error:', error)
      setSubmitStatus('error')
      
      // Clear error message after 5 seconds
      setTimeout(() => {
        setSubmitStatus(null)
      }, 5000)
    } finally {
      setIsSubmitting(false)
    }
  }

  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6 }
    }
  }

  const staggerContainer = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: 0.2
      }
    }
  }

  const contactInfo = [
    {
      icon: <FaMapMarkerAlt className="text-2xl text-primary" />,
      title: "Our Location",
      details: [
        "123 Main Street, Midrand",
        "Gauteng, South Africa, 1685"
      ],
      action: {
        text: "Get Directions",
        url: "https://maps.google.com/?q=Midrand,South+Africa"
      }
    },
    {
      icon: <FaPhone className="text-2xl text-primary" />,
      title: "Phone Number",
      details: [
        "+27 11 123 4567",
        "+27 82 987 6543"
      ],
      action: {
        text: "Call Us",
        url: "tel:+***********"
      }
    },
    {
      icon: <FaWhatsapp className="text-2xl text-primary" />,
      title: "WhatsApp",
      details: [
        "+27 82 987 6543",
        "Quick responses during business hours"
      ],
      action: {
        text: "Message Us",
        url: "https://wa.me/***********"
      }
    },
    {
      icon: <FaEnvelope className="text-2xl text-primary" />,
      title: "Email Address",
      details: [
        "<EMAIL>",
        "<EMAIL>"
      ],
      action: {
        text: "Send Email",
        url: "mailto:<EMAIL>"
      }
    },
    {
      icon: <FaClock className="text-2xl text-primary" />,
      title: "Business Hours",
      details: [
        "Monday - Friday: 8:30 AM - 5:30 PM",
        "Saturday: 9:00 AM - 3:00 PM",
        "Sunday: Closed"
      ]
    }
  ]

  return (
    <>
      <Helmet>
        <title>Contact Us - SMSMali Pawnshop</title>
        <meta name="description" content="Get in touch with SMSMali Pawnshop in Midrand. Visit our store, call us, or send a message for inquiries about our pawnshop services." />
      </Helmet>

      {/* Hero Section */}
      <section className="relative bg-gradient-to-b from-primary/10 to-base-100 py-20 md:py-28">
        <div className="container mx-auto px-4">
          <motion.div 
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="max-w-4xl mx-auto text-center"
          >
            <motion.h1 
              variants={fadeIn}
              className="text-4xl md:text-5xl font-bold mb-6 text-gradient-primary"
            >
              Contact Us
            </motion.h1>
            <motion.p 
              variants={fadeIn}
              className="text-lg md:text-xl mb-8 text-base-content/80"
            >
              We're here to help with all your pawnshop needs
            </motion.p>
          </motion.div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="py-16 bg-base-100">
        <div className="container mx-auto px-4">
          <motion.div 
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16"
          >
            {contactInfo.map((item, index) => (
              <motion.div 
                key={index}
                variants={fadeIn}
                className="bg-base-200 p-6 rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300"
              >
                <div className="flex items-center mb-4">
                  <div className="mr-4 bg-primary/10 p-3 rounded-full">
                    {item.icon}
                  </div>
                  <h3 className="text-xl font-bold">{item.title}</h3>
                </div>
                <div className="mb-4">
                  {item.details.map((detail, i) => (
                    <p key={i} className="text-base-content/80 mb-1">{detail}</p>
                  ))}
                </div>
                {item.action && (
                  <a 
                    href={item.action.url} 
                    target={item.action.url.startsWith('http') ? "_blank" : "_self"}
                    rel={item.action.url.startsWith('http') ? "noopener noreferrer" : ""}
                    className="text-primary hover:text-primary-focus font-medium inline-flex items-center"
                  >
                    {item.action.text}
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </a>
                )}
              </motion.div>
            ))}
          </motion.div>

          <motion.div 
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 lg:grid-cols-2 gap-12"
          >
            {/* Contact Form */}
            <motion.div variants={fadeIn}>
              <h2 className="text-3xl font-bold mb-6">Send Us a Message</h2>
              <p className="mb-8 text-base-content/80">Have a question or need more information? Fill out the form below and we'll get back to you as soon as possible.</p>
              
              {submitStatus === 'success' && (
                <div className="alert alert-success mb-6">
                  <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                  <span>Your message has been sent successfully! We'll get back to you soon.</span>
                </div>
              )}
              
              {submitStatus === 'error' && (
                <div className="alert alert-error mb-6">
                  <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                  <span>There was an error sending your message. Please try again later.</span>
                </div>
              )}
              
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="form-control w-full">
                    <label className="label">
                      <span className="label-text">Full Name</span>
                    </label>
                    <input 
                      type="text" 
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      placeholder="Your full name" 
                      className="input input-bordered w-full" 
                      required 
                    />
                  </div>
                  
                  <div className="form-control w-full">
                    <label className="label">
                      <span className="label-text">Email Address</span>
                    </label>
                    <input 
                      type="email" 
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      placeholder="Your email address" 
                      className="input input-bordered w-full" 
                      required 
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="form-control w-full">
                    <label className="label">
                      <span className="label-text">Phone Number</span>
                    </label>
                    <input 
                      type="tel" 
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      placeholder="Your phone number" 
                      className="input input-bordered w-full" 
                    />
                  </div>
                  
                  <div className="form-control w-full">
                    <label className="label">
                      <span className="label-text">Subject</span>
                    </label>
                    <select 
                      name="subject"
                      value={formData.subject}
                      onChange={handleChange}
                      className="select select-bordered w-full" 
                      required
                    >
                      <option value="" disabled>Select a subject</option>
                      <option value="Pawn Loan Inquiry">Pawn Loan Inquiry</option>
                      <option value="Selling Items">Selling Items</option>
                      <option value="Buying Items">Buying Items</option>
                      <option value="Appraisal Services">Appraisal Services</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>
                </div>
                
                <div className="form-control w-full">
                  <label className="label">
                    <span className="label-text">Message</span>
                  </label>
                  <textarea 
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    placeholder="Your message" 
                    className="textarea textarea-bordered w-full h-32" 
                    required
                  ></textarea>
                </div>
                
                <button 
                  type="submit" 
                  className={`btn btn-primary ${isSubmitting ? 'loading' : ''}`}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Sending...' : 'Send Message'}
                </button>
              </form>
            </motion.div>
            
            {/* Map */}
            <motion.div variants={fadeIn}>
              <h2 className="text-3xl font-bold mb-6">Visit Our Store</h2>
              <p className="mb-8 text-base-content/80">We're conveniently located in Midrand. Stop by during business hours to speak with our experts in person.</p>
              
              <div className="h-96 bg-base-200 rounded-lg overflow-hidden shadow-md">
                <iframe 
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d57370.766031438725!2d28.09977975820313!3d-25.989344899999998!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1e9569d5491e4509%3A0x4fbe2bc0d9d88c3f!2sMidrand%2C%20Johannesburg!5e0!3m2!1sen!2sza!4v1651234567890!5m2!1sen!2sza" 
                  width="100%" 
                  height="100%" 
                  style={{ border: 0 }} 
                  allowFullScreen="" 
                  loading="lazy" 
                  referrerPolicy="no-referrer-when-downgrade"
                  title="SMSMali Pawnshop Location"
                ></iframe>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-base-200">
        <div className="container mx-auto px-4">
          <motion.div 
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="max-w-3xl mx-auto"
          >
            <motion.h2 
              variants={fadeIn}
              className="text-3xl font-bold mb-8 text-center"
            >
              Frequently Asked Questions
            </motion.h2>

            <div className="space-y-4">
              {[
                {
                  question: "What information do I need to bring for a pawn loan?",
                  answer: "You'll need a valid government-issued ID (such as a driver's license or ID card), proof of ownership for the item when applicable, and the item you wish to pawn in good working condition."
                },
                {
                  question: "How quickly can I get cash for my items?",
                  answer: "The entire process typically takes 15-30 minutes, depending on the item and how busy we are. Once we complete the evaluation and paperwork, you'll receive cash immediately."
                },
                {
                  question: "Do you offer free evaluations?",
                  answer: "Yes, we provide free evaluations for all items, with no obligation to accept our offer. Our expert appraisers will assess your items and provide a fair market value."
                },
                {
                  question: "Can I sell my items instead of pawning them?",
                  answer: "Absolutely! We offer both pawn loans and outright purchases. If you prefer to sell your item rather than pawn it, we'll make you a competitive offer based on its current market value."
                }
              ].map((faq, index) => (
                <motion.div 
                  key={index}
                  variants={fadeIn}
                  className="collapse collapse-plus bg-base-100"
                >
                  <input type="radio" name="contact-faq-accordion" /> 
                  <div className="collapse-title text-xl font-medium">
                    {faq.question}
                  </div>
                  <div className="collapse-content"> 
                    <p>{faq.answer}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>
    </>
  )
}

export default ContactPage