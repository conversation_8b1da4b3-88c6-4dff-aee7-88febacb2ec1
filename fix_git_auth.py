#!/usr/bin/env python3
"""
Git Authentication Fix for GitHub
=================================

This script helps fix GitHub authentication issues by providing
step-by-step instructions and automating some configuration.

Usage:
    python fix_git_auth.py

Author: SMSMali Development Team
"""

import subprocess
import sys
import os
from pathlib import Path

class GitAuthFixer:
    def __init__(self):
        self.repo_dir = Path(__file__).resolve().parent
        print("🔐 Git Authentication Helper")
        print("=" * 30)

    def check_git_status(self):
        """Check current Git configuration"""
        print("\n🔍 Checking current Git configuration...")
        
        try:
            # Check remote URLs
            result = subprocess.run(['git', 'remote', '-v'], 
                                  capture_output=True, text=True, cwd=self.repo_dir)
            if result.returncode == 0:
                print("📍 Current remotes:")
                for line in result.stdout.strip().split('\n'):
                    if line:
                        print(f"   {line}")
                return result.stdout
            else:
                print("   ❌ Not a Git repository or no remotes configured")
                return None
        except FileNotFoundError:
            print("   ❌ Git not found. Please install Git first.")
            return None

    def show_pat_instructions(self):
        """Show Personal Access Token instructions"""
        print("\n🔑 PERSONAL ACCESS TOKEN SETUP")
        print("=" * 35)
        print("1. Go to: https://github.com/settings/tokens")
        print("2. Click 'Generate new token (classic)'")
        print("3. Give it a name: 'SMSMali Deployment'")
        print("4. Select scopes:")
        print("   ✅ repo (Full control of private repositories)")
        print("   ✅ workflow (Update GitHub Action workflows)")
        print("5. Click 'Generate token'")
        print("6. 📋 COPY THE TOKEN IMMEDIATELY (you won't see it again)")
        print("\n💡 When Git asks for password, use your token instead!")

    def configure_credential_helper(self):
        """Configure Git credential helper"""
        print("\n⚙️  CONFIGURING CREDENTIAL HELPER")
        print("=" * 35)
        
        try:
            # Set credential helper
            subprocess.run(['git', 'config', '--global', 'credential.helper', 'store'], 
                          check=True, cwd=self.repo_dir)
            print("✅ Credential helper configured")
            
            # Show credential file location
            home = Path.home()
            cred_file = home / '.git-credentials'
            print(f"📁 Credentials will be stored in: {cred_file}")
            print("⚠️  This file will contain your token in plain text")
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to configure credential helper: {e}")

    def show_ssh_setup(self):
        """Show SSH setup instructions"""
        print("\n🔐 SSH AUTHENTICATION SETUP (Recommended)")
        print("=" * 45)
        print("SSH is more secure than HTTPS with tokens.")
        print("\n1. Generate SSH key:")
        print("   ssh-keygen -t ed25519 -C '<EMAIL>'")
        print("   (Press Enter for default location)")
        print("\n2. Copy public key:")
        print("   cat ~/.ssh/id_ed25519.pub")
        print("\n3. Add to GitHub:")
        print("   • Go to: https://github.com/settings/ssh")
        print("   • Click 'New SSH key'")
        print("   • Paste your public key")
        print("\n4. Change remote URL to SSH:")
        
        # Try to suggest SSH URL
        try:
            result = subprocess.run(['git', 'remote', 'get-url', 'origin'], 
                                  capture_output=True, text=True, cwd=self.repo_dir)
            if result.returncode == 0:
                https_url = result.stdout.strip()
                if 'https://github.com/' in https_url:
                    ssh_url = https_url.replace('https://github.com/', '**************:')
                    if not ssh_url.endswith('.git'):
                        ssh_url += '.git'
                    print(f"   git remote set-url origin {ssh_url}")
                else:
                    print("   git remote set-<NAME_EMAIL>:username/repo.git")
        except:
            print("   git remote set-<NAME_EMAIL>:username/repo.git")

    def test_authentication(self):
        """Test Git authentication"""
        print("\n🧪 TESTING AUTHENTICATION")
        print("=" * 25)
        
        try:
            # Try to fetch from remote
            result = subprocess.run(['git', 'fetch', '--dry-run'], 
                                  capture_output=True, text=True, cwd=self.repo_dir)
            if result.returncode == 0:
                print("✅ Authentication successful!")
                return True
            else:
                print("❌ Authentication failed")
                if result.stderr:
                    print(f"   Error: {result.stderr}")
                return False
        except subprocess.CalledProcessError as e:
            print(f"❌ Test failed: {e}")
            return False

    def show_quick_fixes(self):
        """Show quick fix options"""
        print("\n⚡ QUICK FIXES")
        print("=" * 15)
        print("1. 🔑 Use Personal Access Token:")
        print("   • Create token at: https://github.com/settings/tokens")
        print("   • Use token as password when prompted")
        print("\n2. 🔐 Switch to SSH (more secure):")
        print("   • Generate SSH key: ssh-keygen -t ed25519")
        print("   • Add to GitHub: https://github.com/settings/ssh")
        print("   • Change remote URL to SSH")
        print("\n3. 💾 Store credentials (less secure):")
        print("   • git config --global credential.helper store")
        print("   • Enter token once, it will be remembered")

    def interactive_setup(self):
        """Interactive setup process"""
        print("\n🤖 INTERACTIVE SETUP")
        print("=" * 20)
        
        choice = input("Choose authentication method:\n1. Personal Access Token\n2. SSH Key\n3. Skip\nEnter choice (1-3): ").strip()
        
        if choice == "1":
            self.show_pat_instructions()
            self.configure_credential_helper()
            print("\n✅ PAT setup complete. Try your Git operation again.")
            
        elif choice == "2":
            self.show_ssh_setup()
            print("\n✅ SSH instructions provided. Complete the setup and try again.")
            
        else:
            print("⏭️  Skipping interactive setup")

    def fix_auth(self):
        """Main authentication fix process"""
        # Check current status
        remote_info = self.check_git_status()
        
        if not remote_info:
            print("❌ Cannot proceed without Git repository")
            return
        
        # Show different solutions
        self.show_quick_fixes()
        self.show_pat_instructions()
        self.show_ssh_setup()
        
        # Interactive setup
        self.interactive_setup()
        
        # Test authentication
        print("\n" + "="*50)
        print("🧪 You can now test authentication with:")
        print("   git fetch")
        print("   git push")
        print("   git pull")

def main():
    fixer = GitAuthFixer()
    fixer.fix_auth()

if __name__ == "__main__":
    main()
