import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import HeroModel from './HeroModel'

const Hero = () => {
  return (
    <section className="relative min-h-screen flex items-center hero-gradient overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-20 left-10 w-64 h-64 bg-primary rounded-full opacity-10 blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-80 h-80 bg-secondary rounded-full opacity-10 blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 z-10 py-20 lg:py-0">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Text Content */}
          <motion.div 
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            className="text-center lg:text-left"
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              <span className="text-primary">Midrand's Premier</span> <br />
              <span className="text-gradient">Pawnshop Experience</span>
            </h1>
            
            <p className="text-lg md:text-xl text-neutral mb-8 max-w-xl mx-auto lg:mx-0">
              Get instant cash loans on your valuables with the best rates in town. 
              SMSMali offers secure, confidential, and professional pawnbroking services.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Link 
                to="/loan-calculator" 
                className="btn btn-primary text-white px-8 py-3"
              >
                Calculate Your Loan
              </Link>
              <Link 
                to="/services" 
                className="btn btn-outline btn-secondary px-8 py-3"
              >
                Our Services
              </Link>
            </div>
            
            {/* Trust Indicators */}
            <div className="mt-12 grid grid-cols-3 gap-4">
              <div className="text-center">
                <h3 className="text-3xl font-bold text-primary mb-1">5+</h3>
                <p className="text-sm text-neutral">Years Experience</p>
              </div>
              <div className="text-center">
                <h3 className="text-3xl font-bold text-primary mb-1">1000+</h3>
                <p className="text-sm text-neutral">Happy Clients</p>
              </div>
              <div className="text-center">
                <h3 className="text-3xl font-bold text-primary mb-1">100%</h3>
                <p className="text-sm text-neutral">Secure Transactions</p>
              </div>
            </div>
          </motion.div>
          
          {/* 3D Model */}
          <motion.div 
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, ease: 'easeOut', delay: 0.2 }}
            className="h-[400px] md:h-[500px] lg:h-[600px] w-full animate-float"
          >
            <HeroModel />
          </motion.div>
        </div>
      </div>
      
      {/* Scroll Indicator */}
      <motion.div 
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ 
          duration: 0.5, 
          ease: 'easeOut', 
          delay: 1.5,
          repeat: Infinity,
          repeatType: 'reverse',
          repeatDelay: 0.2
        }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex flex-col items-center"
      >
        <p className="text-neutral mb-2 text-sm">Scroll Down</p>
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          width="24" 
          height="24" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="currentColor" 
          strokeWidth="2" 
          strokeLinecap="round" 
          strokeLinejoin="round" 
          className="text-primary"
        >
          <path d="M12 5v14"></path>
          <path d="m19 12-7 7-7-7"></path>
        </svg>
      </motion.div>
    </section>
  )
}

export default Hero