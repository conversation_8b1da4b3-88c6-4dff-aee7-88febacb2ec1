#!/usr/bin/env python3
"""
Frontend Deployment Script for SMSMali
======================================

This script builds the React frontend and integrates it with Django
for production deployment on PythonAnywhere.

Usage:
    python deploy_frontend.py [--build] [--copy-only]

Author: SMSMali Development Team
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import argparse

class FrontendDeployer:
    def __init__(self):
        self.project_root = Path.cwd()
        self.frontend_dir = self.project_root / "frontend"
        self.dist_dir = self.frontend_dir / "dist"
        self.static_dir = self.project_root / "static"
        self.staticfiles_dir = self.project_root / "staticfiles"
        
        print("🎨 SMSMali Frontend Deployment")
        print(f"📁 Project Root: {self.project_root}")
        print(f"⚛️ Frontend Dir: {self.frontend_dir}")
        print("-" * 50)

    def check_frontend_exists(self):
        """Check if frontend directory exists"""
        if not self.frontend_dir.exists():
            print("❌ Frontend directory not found!")
            return False
        
        package_json = self.frontend_dir / "package.json"
        if not package_json.exists():
            print("❌ package.json not found in frontend directory!")
            return False
        
        print("✅ Frontend directory found")
        return True

    def build_frontend(self):
        """Build the React frontend"""
        print("\n🔨 Building React frontend...")
        
        try:
            # Change to frontend directory
            os.chdir(self.frontend_dir)
            
            # Install dependencies if node_modules doesn't exist
            if not (self.frontend_dir / "node_modules").exists():
                print("📦 Installing npm dependencies...")
                subprocess.run(["npm", "install"], check=True)
            
            # Build the frontend
            print("🏗️ Building production build...")
            subprocess.run(["npm", "run", "build"], check=True)
            
            # Change back to project root
            os.chdir(self.project_root)
            
            print("✅ Frontend build completed")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Frontend build failed: {e}")
            return False
        except FileNotFoundError:
            print("❌ npm not found. Please install Node.js and npm")
            return False
        finally:
            # Ensure we're back in project root
            os.chdir(self.project_root)

    def copy_frontend_files(self):
        """Copy built frontend files to Django static directories"""
        print("\n📋 Copying frontend files...")
        
        if not self.dist_dir.exists():
            print("❌ Frontend dist directory not found. Run build first.")
            return False
        
        try:
            # Create static directory if it doesn't exist
            self.static_dir.mkdir(exist_ok=True)
            
            # Copy index.html to templates directory (for Django to serve)
            templates_dir = self.project_root / "templates"
            templates_dir.mkdir(exist_ok=True)
            
            index_html_src = self.dist_dir / "index.html"
            index_html_dst = templates_dir / "index.html"
            
            if index_html_src.exists():
                shutil.copy2(index_html_src, index_html_dst)
                print(f"✅ Copied index.html to {index_html_dst}")
            
            # Copy assets to static directory
            assets_src = self.dist_dir / "assets"
            assets_dst = self.static_dir / "assets"
            
            if assets_src.exists():
                if assets_dst.exists():
                    shutil.rmtree(assets_dst)
                shutil.copytree(assets_src, assets_dst)
                print(f"✅ Copied assets to {assets_dst}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error copying files: {e}")
            return False

    def collect_static_files(self):
        """Run Django's collectstatic command"""
        print("\n📁 Collecting Django static files...")
        
        try:
            # Try to use virtual environment python first
            venv_python = self.project_root / "venv" / "bin" / "python"
            if not venv_python.exists():
                venv_python = "python3.10"  # Fallback for PythonAnywhere
            
            manage_py = self.project_root / "manage.py"
            if not manage_py.exists():
                print("❌ manage.py not found")
                return False
            
            # Run collectstatic
            subprocess.run([
                str(venv_python), str(manage_py), 
                "collectstatic", "--noinput", "--clear"
            ], check=True)
            
            print("✅ Static files collected")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ collectstatic failed: {e}")
            return False

    def verify_deployment(self):
        """Verify that all files are in place"""
        print("\n🔍 Verifying deployment...")
        
        checks = [
            (self.project_root / "templates" / "index.html", "React index.html in templates"),
            (self.static_dir / "assets", "Frontend assets in static"),
            (self.staticfiles_dir, "Django staticfiles directory"),
        ]
        
        all_good = True
        for file_path, description in checks:
            if file_path.exists():
                print(f"✅ {description}")
            else:
                print(f"❌ Missing: {description}")
                all_good = False
        
        if all_good:
            print("\n🎉 Frontend deployment verification passed!")
        else:
            print("\n⚠️ Some files are missing. Check the deployment.")
        
        return all_good

    def deploy(self, build=True, copy_only=False):
        """Main deployment process"""
        if not self.check_frontend_exists():
            return False
        
        success = True
        
        if build and not copy_only:
            success = success and self.build_frontend()
        
        if success:
            success = success and self.copy_frontend_files()
        
        if success and not copy_only:
            success = success and self.collect_static_files()
        
        if success:
            self.verify_deployment()
        
        return success

def main():
    parser = argparse.ArgumentParser(description='Deploy SMSMali React frontend')
    parser.add_argument('--build', action='store_true', default=True,
                       help='Build the frontend before copying')
    parser.add_argument('--copy-only', action='store_true',
                       help='Only copy files, skip build and collectstatic')
    parser.add_argument('--no-build', action='store_true',
                       help='Skip building, only copy existing dist files')
    
    args = parser.parse_args()
    
    # Handle conflicting arguments
    if args.no_build:
        args.build = False
    
    deployer = FrontendDeployer()
    success = deployer.deploy(build=args.build, copy_only=args.copy_only)
    
    if success:
        print("\n✅ Frontend deployment completed successfully!")
        print("\n📋 Next steps:")
        print("1. Restart your Django development server")
        print("2. Visit your site to see the React frontend")
        print("3. For PythonAnywhere: reload your web app")
        return 0
    else:
        print("\n❌ Frontend deployment failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
