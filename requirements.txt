# Django and Django REST Framework
Django>=4.2,<5.0
djangorestframework>=3.14
djangorestframework-simplejwt>=5.3
django-cors-headers>=4.3
django-filter>=23.0  # For filtering support

# Image Processing
Pillow>=10.0  # Latest stable version

# Database
psycopg2-binary>=2.9  # For PostgreSQL (optional)

# Development and Deployment
whitenoise>=6.6  # For serving static files
gunicorn>=21.0  # For production deployment
python-dotenv>=1.0  # For environment variables

# Utilities
python-slugify>=8.0  # For generating slugs
drf-yasg>=1.21  # For API documentation

# Additional production dependencies
certifi>=2023.0
charset-normalizer>=3.0
idna>=3.0
requests>=2.31
urllib3>=2.0