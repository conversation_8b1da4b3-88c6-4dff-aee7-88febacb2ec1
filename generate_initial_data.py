import os
import django
import random
from datetime import datetime, timedelta
from django.utils.text import slugify

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smsmali.settings')
django.setup()

# Import models after Django setup
from django.contrib.auth.models import User
from products.models import Category, Product, ProductImage, ProductSpecification, Review
from loans.models import ItemType, LoanTerm, LoanCalculation
from users.models import Profile, Newsletter

def create_superuser():
    """Create a superuser if it doesn't exist"""
    if not User.objects.filter(username='admin').exists():
        User.objects.create_superuser('admin', '<EMAIL>', 'adminpassword')
        print('Superuser created')
    else:
        print('Superuser already exists')

def create_categories():
    """Create product categories"""
    categories = [
        {'name': 'Jewelry', 'description': 'Fine jewelry including gold, silver, and diamond pieces'},
        {'name': 'Electronics', 'description': 'Smartphones, laptops, tablets, and other electronic devices'},
        {'name': 'Watches', 'description': 'Luxury and designer watches'},
        {'name': 'Musical Instruments', 'description': 'Guitars, keyboards, and other musical instruments'},
        {'name': 'Tools', 'description': 'Power tools and equipment'},
        {'name': 'Collectibles', 'description': 'Rare items and collectibles'},
    ]
    
    for category_data in categories:
        Category.objects.get_or_create(
            name=category_data['name'],
            defaults={
                'description': category_data['description'],
                'slug': slugify(category_data['name'])
            }
        )
    
    print(f'{len(categories)} categories created')

def create_products():
    """Create sample products"""
    if Product.objects.exists():
        print('Products already exist, skipping creation')
        return
    
    categories = Category.objects.all()
    conditions = ['New', 'Like New', 'Good', 'Fair', 'Poor']
    
    products = [
        # Jewelry
        {
            'name': 'Gold Chain Necklace',
            'description': '18K gold chain necklace, 20 inches long',
            'price': 1200.00,
            'discount_price': 1080.00,
            'category': 'Jewelry',
            'condition': 'Like New',
            'stock': 2,
            'specifications': [
                {'name': 'Material', 'value': '18K Gold'},
                {'name': 'Length', 'value': '20 inches'},
                {'name': 'Weight', 'value': '15 grams'},
            ]
        },
        {
            'name': 'Diamond Engagement Ring',
            'description': '1 carat diamond solitaire ring with platinum band',
            'price': 3500.00,
            'discount_price': None,
            'category': 'Jewelry',
            'condition': 'Good',
            'stock': 1,
            'specifications': [
                {'name': 'Diamond Size', 'value': '1 carat'},
                {'name': 'Band Material', 'value': 'Platinum'},
                {'name': 'Clarity', 'value': 'VS1'},
                {'name': 'Color', 'value': 'F'},
            ]
        },
        
        # Electronics
        {
            'name': 'iPhone 13 Pro',
            'description': 'Apple iPhone 13 Pro, 256GB storage, Sierra Blue',
            'price': 899.99,
            'discount_price': 799.99,
            'category': 'Electronics',
            'condition': 'Good',
            'stock': 3,
            'specifications': [
                {'name': 'Storage', 'value': '256GB'},
                {'name': 'Color', 'value': 'Sierra Blue'},
                {'name': 'Screen Size', 'value': '6.1 inches'},
                {'name': 'Battery Health', 'value': '92%'},
            ]
        },
        {
            'name': 'MacBook Pro 2021',
            'description': 'Apple MacBook Pro with M1 Pro chip, 16GB RAM, 512GB SSD',
            'price': 1599.99,
            'discount_price': 1499.99,
            'category': 'Electronics',
            'condition': 'Like New',
            'stock': 2,
            'specifications': [
                {'name': 'Processor', 'value': 'M1 Pro'},
                {'name': 'RAM', 'value': '16GB'},
                {'name': 'Storage', 'value': '512GB SSD'},
                {'name': 'Screen Size', 'value': '14 inches'},
            ]
        },
        
        # Watches
        {
            'name': 'Rolex Submariner',
            'description': 'Rolex Submariner Date, stainless steel with black dial',
            'price': 9500.00,
            'discount_price': None,
            'category': 'Watches',
            'condition': 'Good',
            'stock': 1,
            'specifications': [
                {'name': 'Movement', 'value': 'Automatic'},
                {'name': 'Case Material', 'value': 'Stainless Steel'},
                {'name': 'Water Resistance', 'value': '300m'},
                {'name': 'Year', 'value': '2019'},
            ]
        },
        
        # Musical Instruments
        {
            'name': 'Fender Stratocaster',
            'description': 'Fender American Professional II Stratocaster Electric Guitar',
            'price': 1200.00,
            'discount_price': 1100.00,
            'category': 'Musical Instruments',
            'condition': 'Good',
            'stock': 1,
            'specifications': [
                {'name': 'Body Material', 'value': 'Alder'},
                {'name': 'Neck Material', 'value': 'Maple'},
                {'name': 'Fretboard', 'value': 'Rosewood'},
                {'name': 'Pickups', 'value': '3 Single-coil'},
            ]
        },
        
        # Tools
        {
            'name': 'DeWalt Power Drill',
            'description': 'DeWalt 20V MAX Cordless Drill/Driver Kit',
            'price': 149.99,
            'discount_price': 129.99,
            'category': 'Tools',
            'condition': 'Good',
            'stock': 4,
            'specifications': [
                {'name': 'Voltage', 'value': '20V'},
                {'name': 'Chuck Size', 'value': '1/2 inch'},
                {'name': 'Includes', 'value': 'Battery, Charger, Case'},
            ]
        },
        
        # Collectibles
        {
            'name': 'Vintage Comic Book',
            'description': 'Rare first edition comic in protective sleeve',
            'price': 450.00,
            'discount_price': None,
            'category': 'Collectibles',
            'condition': 'Fair',
            'stock': 1,
            'specifications': [
                {'name': 'Year', 'value': '1975'},
                {'name': 'Condition Grade', 'value': '7.5/10'},
                {'name': 'Publisher', 'value': 'Marvel'},
            ]
        },
    ]
    
    for product_data in products:
        category = Category.objects.get(name=product_data['category'])
        
        product = Product.objects.create(
            name=product_data['name'],
            description=product_data['description'],
            price=product_data['price'],
            discount_price=product_data['discount_price'],
            category=category,
            condition=product_data['condition'],
            stock=product_data['stock'],
            slug=slugify(product_data['name']),
            is_featured=random.choice([True, False]),
        )
        
        # Add specifications
        for spec in product_data['specifications']:
            ProductSpecification.objects.create(
                product=product,
                name=spec['name'],
                value=spec['value']
            )
        
        # Add a placeholder image
        ProductImage.objects.create(
            product=product,
            image='products/placeholder.svg',  # SVG placeholder image
            is_primary=True
        )
    
    print(f'{len(products)} products created')

def create_item_types():
    """Create loan item types"""
    item_types = [
        {'name': 'Gold Jewelry', 'description': 'Gold rings, necklaces, bracelets', 'loan_to_value': 80},
        {'name': 'Silver Jewelry', 'description': 'Silver rings, necklaces, bracelets', 'loan_to_value': 70},
        {'name': 'Diamonds', 'description': 'Diamond jewelry and loose diamonds', 'loan_to_value': 75},
        {'name': 'Luxury Watches', 'description': 'Rolex, Omega, Tag Heuer, etc.', 'loan_to_value': 70},
        {'name': 'Electronics', 'description': 'Smartphones, laptops, tablets', 'loan_to_value': 60},
        {'name': 'Musical Instruments', 'description': 'Guitars, keyboards, etc.', 'loan_to_value': 65},
        {'name': 'Power Tools', 'description': 'Professional grade tools', 'loan_to_value': 50},
        {'name': 'Collectibles', 'description': 'Coins, stamps, memorabilia', 'loan_to_value': 55},
    ]
    
    for item_data in item_types:
        ItemType.objects.get_or_create(
            name=item_data['name'],
            defaults={
                'description': item_data['description'],
                'loan_to_value': item_data['loan_to_value']
            }
        )
    
    print(f'{len(item_types)} item types created')

def create_loan_terms():
    """Create loan terms"""
    loan_terms = [
        {'months': 1, 'interest_rate': 5.0, 'description': '1 month term with 5% interest'},
        {'months': 3, 'interest_rate': 12.0, 'description': '3 month term with 12% interest'},
        {'months': 6, 'interest_rate': 20.0, 'description': '6 month term with 20% interest'},
        {'months': 12, 'interest_rate': 35.0, 'description': '12 month term with 35% interest'},
    ]
    
    for term_data in loan_terms:
        LoanTerm.objects.get_or_create(
            months=term_data['months'],
            defaults={
                'interest_rate': term_data['interest_rate'],
                'description': term_data['description']
            }
        )
    
    print(f'{len(loan_terms)} loan terms created')

def create_loan_calculations():
    """Create sample loan calculations"""
    item_types = ItemType.objects.all()
    loan_terms = LoanTerm.objects.all()
    
    # Create a few sample loan calculations
    for i in range(5):
        item_type = random.choice(item_types)
        loan_term = random.choice(loan_terms)
        item_value = random.randint(500, 5000)
        
        LoanCalculation.objects.create(
            item_type=item_type,
            loan_term=loan_term,
            item_value=item_value,
            loan_amount=item_value * (item_type.loan_to_value / 100),
            interest_amount=(item_value * (item_type.loan_to_value / 100)) * (loan_term.interest_rate / 100),
            total_repayment=(item_value * (item_type.loan_to_value / 100)) * (1 + (loan_term.interest_rate / 100))
        )
    
    print('5 sample loan calculations created')

def main():
    print('Starting data generation...')
    create_superuser()
    create_categories()
    create_products()
    create_item_types()
    create_loan_terms()
    create_loan_calculations()
    print('Data generation complete!')

if __name__ == '__main__':
    main()