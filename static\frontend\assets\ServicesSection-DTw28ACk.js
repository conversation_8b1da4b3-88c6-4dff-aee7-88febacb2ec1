import{j as e}from"./three-DVx3d6Kc.js";import{F as r,a as n,b as l,c as o,d as c}from"./index-DwaqOzzB.js";import{L as d}from"./router-By-Xbj7l.js";import{m as t}from"./ui-C4lKJJfU.js";import"./vendor-DWLLDKvm.js";const m=[{id:"loans",icon:e.jsx(r,{className:"text-4xl text-secondary"}),title:"Pawn Loans",description:"Get instant cash loans on your valuables with competitive interest rates and flexible terms."},{id:"buying",icon:e.jsx(n,{className:"text-4xl text-secondary"}),title:"Buying Valuables",description:"We buy gold, jewelry, electronics, and other valuable items at the best market prices."},{id:"selling",icon:e.jsx(l,{className:"text-4xl text-secondary"}),title:"Selling Items",description:"Browse our collection of quality pre-owned items at affordable prices with warranty."},{id:"appraisal",icon:e.jsx(o,{className:"text-4xl text-secondary"}),title:"Item Appraisal",description:"Get professional appraisal services for your valuable items from our experienced team."},{id:"jewelry",icon:e.jsx(c,{className:"text-4xl text-secondary"}),title:"Jewelry Cleaning",description:"Professional jewelry cleaning and minor repair services to keep your items looking their best."}],v=()=>{const a={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},s={hidden:{y:20,opacity:0},visible:{y:0,opacity:1,transition:{duration:.5,ease:"easeOut"}}};return e.jsx("section",{id:"services",className:"py-20 bg-base-100",children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsxs(t.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0,margin:"-100px"},transition:{duration:.5},className:"text-center mb-16",children:[e.jsxs("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:["Our ",e.jsx("span",{className:"text-primary",children:"Services"})]}),e.jsx("p",{className:"text-neutral max-w-2xl mx-auto",children:"SMSMali offers a comprehensive range of pawnbroking services to meet your financial needs and help you find quality pre-owned items."})]}),e.jsx(t.div,{variants:a,initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:m.map(i=>e.jsx(t.div,{variants:s,className:"card bg-base-100 shadow-lg hover:shadow-xl card-hover",children:e.jsxs("div",{className:"card-body text-center",children:[e.jsx("div",{className:"flex justify-center mb-4",children:i.icon}),e.jsx("h3",{className:"card-title text-xl font-bold justify-center mb-2",children:i.title}),e.jsx("p",{className:"text-neutral",children:i.description})]})},i.id))}),e.jsx(t.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0,margin:"-100px"},transition:{duration:.5,delay:.6},className:"text-center mt-12",children:e.jsx(d,{to:"/services",className:"btn btn-primary text-white px-8",children:"View All Services"})})]})})};export{v as default};
