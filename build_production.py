#!/usr/bin/env python3
"""
Production build script for SMSMali Pawnshop Application
This script builds the frontend and prepares the application for production deployment
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, cwd=None):
    """Run a shell command and return the result"""
    try:
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True,
            check=True
        )
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {command}")
        print(f"Error output: {e.stderr}")
        return None

def main():
    # Get the project root directory
    project_root = Path(__file__).parent
    frontend_dir = project_root / "frontend"
    static_dir = project_root / "static"
    
    print("🚀 Starting production build for SMSMali...")
    
    # Check if frontend directory exists
    if not frontend_dir.exists():
        print("❌ Frontend directory not found!")
        sys.exit(1)
    
    # Check if Node.js is available
    if not run_command("node --version"):
        print("❌ Node.js not found! Please install Node.js first.")
        sys.exit(1)
    
    # Check if npm is available
    if not run_command("npm --version"):
        print("❌ npm not found! Please install npm first.")
        sys.exit(1)
    
    print("✅ Node.js and npm are available")
    
    # Create static directory if it doesn't exist
    static_dir.mkdir(exist_ok=True)
    
    # Navigate to frontend directory and install dependencies
    print("📦 Installing frontend dependencies...")
    if not run_command("npm install", cwd=frontend_dir):
        print("❌ Failed to install frontend dependencies")
        sys.exit(1)
    
    print("✅ Frontend dependencies installed")
    
    # Build the frontend
    print("🔨 Building frontend for production...")
    if not run_command("npm run build", cwd=frontend_dir):
        print("❌ Failed to build frontend")
        sys.exit(1)
    
    print("✅ Frontend built successfully")
    
    # Verify build output
    frontend_build_dir = static_dir / "frontend"
    if not frontend_build_dir.exists():
        print("❌ Frontend build output not found!")
        sys.exit(1)
    
    print("✅ Frontend build output verified")
    
    # Create staticfiles directory for Django
    staticfiles_dir = project_root / "staticfiles"
    staticfiles_dir.mkdir(exist_ok=True)
    
    print("📁 Static files directory created")
    
    # Create logs directory
    logs_dir = project_root / "logs"
    logs_dir.mkdir(exist_ok=True)
    
    print("📝 Logs directory created")
    
    print("🎉 Production build completed successfully!")
    print("\nNext steps:")
    print("1. Set up environment variables (.env file)")
    print("2. Run migrations: python manage_production.py migrate")
    print("3. Collect static files: python manage_production.py collectstatic")
    print("4. Create superuser: python manage_production.py createsuperuser")
    print("5. Deploy to PythonAnywhere")

if __name__ == "__main__":
    main()