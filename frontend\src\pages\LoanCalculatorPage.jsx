import { useState } from 'react'
import { motion } from 'framer-motion'
import { Helmet } from 'react-helmet'
import { FaCalculator, FaInfoCircle, FaMoneyBillWave, FaCalendarAlt, FaPercentage, FaArrowRight } from 'react-icons/fa'

const LoanCalculatorPage = () => {
  const [itemType, setItemType] = useState('jewelry')
  const [itemValue, setItemValue] = useState(5000)
  const [loanTerm, setLoanTerm] = useState(30)
  const [calculationResult, setCalculationResult] = useState(null)
  
  // Loan-to-value ratios for different item types
  const ltvRatios = {
    jewelry: 0.7, // 70% of appraised value
    watches: 0.65,
    electronics: 0.5,
    luxury: 0.75
  }
  
  // Interest rates for different terms
  const interestRates = {
    30: 0.05, // 5% for 30 days
    60: 0.09, // 9% for 60 days
    90: 0.12  // 12% for 90 days
  }
  
  const handleCalculate = () => {
    // Calculate loan amount based on item value and LTV ratio
    const loanAmount = itemValue * ltvRatios[itemType]
    
    // Calculate interest amount
    const interestAmount = loanAmount * interestRates[loanTerm]
    
    // Calculate total repayment amount
    const totalRepayment = loanAmount + interestAmount
    
    // Calculate monthly payment (if applicable)
    const monthlyPayment = loanTerm === 30 ? totalRepayment : totalRepayment / (loanTerm / 30)
    
    setCalculationResult({
      loanAmount,
      interestAmount,
      totalRepayment,
      monthlyPayment,
      interestRate: interestRates[loanTerm] * 100
    })
  }
  
  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 2
    }).format(amount)
  }
  
  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6 }
    }
  }

  const staggerContainer = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: 0.2
      }
    }
  }

  return (
    <>
      <Helmet>
        <title>Loan Calculator - SMSMali Pawnshop</title>
        <meta name="description" content="Use our loan calculator to estimate how much cash you can get for your valuables at SMSMali Pawnshop in Midrand." />
      </Helmet>

      {/* Hero Section */}
      <section className="relative bg-gradient-to-b from-primary/10 to-base-100 py-20 md:py-28">
        <div className="container mx-auto px-4">
          <motion.div 
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="max-w-4xl mx-auto text-center"
          >
            <motion.h1 
              variants={fadeIn}
              className="text-4xl md:text-5xl font-bold mb-6 text-gradient-primary"
            >
              Loan Calculator
            </motion.h1>
            <motion.p 
              variants={fadeIn}
              className="text-lg md:text-xl mb-8 text-base-content/80"
            >
              Estimate how much cash you can get for your valuables
            </motion.p>
          </motion.div>
        </div>
      </section>

      {/* Calculator Section */}
      <section className="py-16 bg-base-100">
        <div className="container mx-auto px-4">
          <motion.div 
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 lg:grid-cols-2 gap-12"
          >
            {/* Calculator Form */}
            <motion.div variants={fadeIn}>
              <div className="bg-base-200 p-8 rounded-lg shadow-md">
                <div className="flex items-center mb-6">
                  <FaCalculator className="text-3xl text-primary mr-4" />
                  <h2 className="text-2xl font-bold">Loan Estimator</h2>
                </div>
                
                <div className="space-y-6">
                  {/* Item Type */}
                  <div>
                    <label className="block text-sm font-medium mb-2">Item Type</label>
                    <select 
                      value={itemType}
                      onChange={(e) => setItemType(e.target.value)}
                      className="select select-bordered w-full"
                    >
                      <option value="jewelry">Jewelry (Gold, Silver, Diamonds)</option>
                      <option value="watches">Watches</option>
                      <option value="electronics">Electronics</option>
                      <option value="luxury">Luxury Items</option>
                    </select>
                    <p className="text-xs text-base-content/70 mt-1">
                      Different items have different loan-to-value ratios
                    </p>
                  </div>
                  
                  {/* Item Value */}
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Estimated Item Value: {formatCurrency(itemValue)}
                    </label>
                    <input 
                      type="range" 
                      min="500" 
                      max="100000" 
                      step="500"
                      value={itemValue}
                      onChange={(e) => setItemValue(Number(e.target.value))}
                      className="range range-primary" 
                    />
                    <div className="flex justify-between text-xs mt-1">
                      <span>R500</span>
                      <span>R25,000</span>
                      <span>R50,000</span>
                      <span>R100,000</span>
                    </div>
                  </div>
                  
                  {/* Loan Term */}
                  <div>
                    <label className="block text-sm font-medium mb-2">Loan Term</label>
                    <div className="flex space-x-4">
                      {[30, 60, 90].map((days) => (
                        <div key={days} className="flex-1">
                          <input 
                            type="radio" 
                            id={`term-${days}`}
                            name="loan-term"
                            className="hidden peer" 
                            checked={loanTerm === days}
                            onChange={() => setLoanTerm(days)}
                          />
                          <label 
                            htmlFor={`term-${days}`}
                            className="block text-center p-3 border rounded-lg cursor-pointer transition-all peer-checked:bg-primary peer-checked:text-primary-content peer-checked:border-primary"
                          >
                            {days} Days
                          </label>
                        </div>
                      ))}
                    </div>
                    <p className="text-xs text-base-content/70 mt-1">
                      Interest rates vary based on the loan term
                    </p>
                  </div>
                  
                  <button 
                    onClick={handleCalculate}
                    className="btn btn-primary w-full"
                  >
                    Calculate Loan Estimate
                  </button>
                </div>
              </div>
            </motion.div>
            
            {/* Results and Information */}
            <motion.div variants={fadeIn}>
              {calculationResult ? (
                <div className="bg-primary text-primary-content p-8 rounded-lg shadow-md">
                  <h3 className="text-xl font-bold mb-6">Your Loan Estimate</h3>
                  
                  <div className="space-y-4">
                    <div className="flex justify-between items-center pb-2 border-b border-primary-content/20">
                      <div className="flex items-center">
                        <FaMoneyBillWave className="mr-2" />
                        <span>Loan Amount:</span>
                      </div>
                      <span className="text-xl font-bold">{formatCurrency(calculationResult.loanAmount)}</span>
                    </div>
                    
                    <div className="flex justify-between items-center pb-2 border-b border-primary-content/20">
                      <div className="flex items-center">
                        <FaPercentage className="mr-2" />
                        <span>Interest Rate:</span>
                      </div>
                      <span>{calculationResult.interestRate}%</span>
                    </div>
                    
                    <div className="flex justify-between items-center pb-2 border-b border-primary-content/20">
                      <div className="flex items-center">
                        <FaCalendarAlt className="mr-2" />
                        <span>Loan Term:</span>
                      </div>
                      <span>{loanTerm} days</span>
                    </div>
                    
                    <div className="flex justify-between items-center pb-2 border-b border-primary-content/20">
                      <div className="flex items-center">
                        <span>Interest Amount:</span>
                      </div>
                      <span>{formatCurrency(calculationResult.interestAmount)}</span>
                    </div>
                    
                    <div className="flex justify-between items-center pb-2 border-b border-primary-content/20">
                      <div className="flex items-center">
                        <span>Total Repayment:</span>
                      </div>
                      <span className="text-xl font-bold">{formatCurrency(calculationResult.totalRepayment)}</span>
                    </div>
                    
                    {loanTerm > 30 && (
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <span>Monthly Payment:</span>
                        </div>
                        <span>{formatCurrency(calculationResult.monthlyPayment)}</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="mt-8 bg-primary-focus p-4 rounded-lg">
                    <div className="flex items-start">
                      <FaInfoCircle className="mt-1 mr-3 flex-shrink-0" />
                      <p className="text-sm">
                        This is just an estimate. The actual loan amount will be determined after our experts evaluate your item in person. Visit our store for an accurate appraisal.
                      </p>
                    </div>
                  </div>
                  
                  <div className="mt-6 text-center">
                    <a href="/contact" className="btn btn-secondary">
                      Contact Us for Actual Appraisal <FaArrowRight className="ml-2" />
                    </a>
                  </div>
                </div>
              ) : (
                <div className="space-y-8">
                  <div className="bg-base-200 p-8 rounded-lg shadow-md">
                    <h3 className="text-xl font-bold mb-4">How Our Loans Work</h3>
                    
                    <ol className="space-y-4">
                      <li className="flex">
                        <span className="bg-primary text-primary-content w-6 h-6 rounded-full flex items-center justify-center mr-3 flex-shrink-0">1</span>
                        <p><strong>Bring your item</strong> to our store for a free, no-obligation evaluation by our experts.</p>
                      </li>
                      <li className="flex">
                        <span className="bg-primary text-primary-content w-6 h-6 rounded-full flex items-center justify-center mr-3 flex-shrink-0">2</span>
                        <p><strong>Receive a loan offer</strong> based on the appraised value of your item.</p>
                      </li>
                      <li className="flex">
                        <span className="bg-primary text-primary-content w-6 h-6 rounded-full flex items-center justify-center mr-3 flex-shrink-0">3</span>
                        <p><strong>Get cash immediately</strong> if you accept our offer.</p>
                      </li>
                      <li className="flex">
                        <span className="bg-primary text-primary-content w-6 h-6 rounded-full flex items-center justify-center mr-3 flex-shrink-0">4</span>
                        <p><strong>Repay the loan plus interest</strong> within the agreed term to reclaim your item.</p>
                      </li>
                    </ol>
                  </div>
                  
                  <div className="bg-base-200 p-8 rounded-lg shadow-md">
                    <h3 className="text-xl font-bold mb-4">Items We Accept</h3>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div className="flex items-start">
                        <div className="bg-primary/10 p-2 rounded-full mr-3">
                          <FaMoneyBillWave className="text-primary" />
                        </div>
                        <div>
                          <h4 className="font-bold">Jewelry</h4>
                          <p className="text-sm text-base-content/70">Gold, silver, diamonds, gemstones</p>
                        </div>
                      </div>
                      
                      <div className="flex items-start">
                        <div className="bg-primary/10 p-2 rounded-full mr-3">
                          <FaMoneyBillWave className="text-primary" />
                        </div>
                        <div>
                          <h4 className="font-bold">Watches</h4>
                          <p className="text-sm text-base-content/70">Luxury and designer watches</p>
                        </div>
                      </div>
                      
                      <div className="flex items-start">
                        <div className="bg-primary/10 p-2 rounded-full mr-3">
                          <FaMoneyBillWave className="text-primary" />
                        </div>
                        <div>
                          <h4 className="font-bold">Electronics</h4>
                          <p className="text-sm text-base-content/70">Smartphones, laptops, cameras</p>
                        </div>
                      </div>
                      
                      <div className="flex items-start">
                        <div className="bg-primary/10 p-2 rounded-full mr-3">
                          <FaMoneyBillWave className="text-primary" />
                        </div>
                        <div>
                          <h4 className="font-bold">Luxury Items</h4>
                          <p className="text-sm text-base-content/70">Designer bags, collectibles</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-base-200">
        <div className="container mx-auto px-4">
          <motion.div 
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="max-w-3xl mx-auto"
          >
            <motion.h2 
              variants={fadeIn}
              className="text-3xl font-bold mb-8 text-center"
            >
              Frequently Asked Questions
            </motion.h2>

            <div className="space-y-4">
              {[
                {
                  question: "How is the value of my item determined?",
                  answer: "Our expert appraisers evaluate your items based on factors such as condition, age, brand, market demand, and current resale value. For precious metals, we consider weight, purity, and current market prices."
                },
                {
                  question: "What happens if I can't repay my loan on time?",
                  answer: "If you need more time, contact us before the due date to discuss extension options. If you cannot repay the loan, the item becomes the property of the pawnshop and may be sold."
                },
                {
                  question: "Can I pay off my loan early?",
                  answer: "Yes, you can repay your loan at any time during the loan period. We calculate interest based on the actual number of days the loan was active, so paying early can save you money."
                },
                {
                  question: "What documents do I need to get a loan?",
                  answer: "You'll need a valid government-issued ID (such as a driver's license or ID card), proof of ownership for the item when applicable, and the item you wish to pawn in good working condition."
                },
                {
                  question: "Is my information kept confidential?",
                  answer: "Yes, we take your privacy seriously. All transactions and personal information are kept strictly confidential in accordance with data protection regulations."
                }
              ].map((faq, index) => (
                <motion.div 
                  key={index}
                  variants={fadeIn}
                  className="collapse collapse-plus bg-base-100"
                >
                  <input type="radio" name="loan-faq-accordion" /> 
                  <div className="collapse-title text-xl font-medium">
                    {faq.question}
                  </div>
                  <div className="collapse-content"> 
                    <p>{faq.answer}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary text-primary-content">
        <div className="container mx-auto px-4 text-center">
          <motion.div 
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="max-w-3xl mx-auto"
          >
            <motion.h2 
              variants={fadeIn}
              className="text-3xl font-bold mb-6"
            >
              Ready to Get Cash for Your Valuables?
            </motion.h2>
            <motion.p 
              variants={fadeIn}
              className="text-lg mb-8"
            >
              Visit our store today for a free, no-obligation appraisal of your items.
            </motion.p>
            <motion.div variants={fadeIn}>
              <a href="/contact" className="btn btn-secondary btn-lg mr-4">Contact Us</a>
              <a href="/services" className="btn btn-outline btn-lg border-white text-white hover:bg-white hover:text-primary">Our Services</a>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </>
  )
}

export default LoanCalculatorPage