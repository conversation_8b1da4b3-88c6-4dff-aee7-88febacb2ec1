# SMSMali MCP Deployment Server Guide

This guide explains how to use the Model Context Protocol (MCP) server for automated SMSMali deployment to PythonAnywhere.

## 🚀 Overview

The MCP deployment server provides a programmatic interface for:
- 📥 Deploying code from Git to PythonAnywhere
- 🔍 Checking deployment status
- ⚙️ Generating web app configuration
- 🐍 Running Django management commands
- 📋 Monitoring Git repository status

## 📦 Installation

### Step 1: Install MCP Dependencies
```bash
# Install MCP and dependencies
pip install -r requirements_mcp.txt

# Or install manually
pip install mcp asyncio-mqtt aiofiles aiohttp
```

### Step 2: Verify Installation
```bash
# Check if MCP is installed
python -c "import mcp; print('MCP installed successfully')"
```

## 🔧 Server Setup

### Start the MCP Server
```bash
# Start the deployment server
python mcp_deployment_server.py
```

The server provides these tools:
- `deploy_from_git` - Deploy from Git repository
- `check_deployment_status` - Check current status
- `update_webapp_config` - Generate PythonAnywhere config
- `run_django_commands` - Execute Django commands
- `check_git_status` - Check Git repository status

## 💻 Client Usage

### Using the Command Line Client

#### 1. Deploy from Git
```bash
# Basic deployment
python mcp_deployment_client.py deploy

# Custom deployment
python mcp_deployment_client.py deploy \
    --git-url https://github.com/CooperSystems-commits/SMSMali.git \
    --target-dir /home/<USER>/SMSMali \
    --production \
    --force-clean
```

#### 2. Check Deployment Status
```bash
# Check status
python mcp_deployment_client.py status

# Check specific directory
python mcp_deployment_client.py status --directory /home/<USER>/SMSMali
```

#### 3. Generate Web App Configuration
```bash
# Generate PythonAnywhere config
python mcp_deployment_client.py config
```

#### 4. Run Django Commands
```bash
# Run default commands (collectstatic, migrate)
python mcp_deployment_client.py django-commands

# Run custom commands
python mcp_deployment_client.py django-commands \
    --commands "collectstatic --noinput" "migrate" "createsuperuser"
```

#### 5. Check Git Status
```bash
# Check Git repository status
python mcp_deployment_client.py git-status
```

#### 6. List Available Tools
```bash
# List all available tools
python mcp_deployment_client.py list-tools
```

## 🔌 API Reference

### Tool: deploy_from_git

Deploy SMSMali from Git repository to PythonAnywhere.

**Parameters:**
- `git_url` (string): Git repository URL
- `target_directory` (string): Target deployment directory
- `production` (boolean): Deploy in production mode
- `force_clean` (boolean): Force clean deployment

**Example:**
```json
{
  "git_url": "https://github.com/CooperSystems-commits/SMSMali.git",
  "target_directory": "/home/<USER>/SMSMali",
  "production": true,
  "force_clean": false
}
```

### Tool: check_deployment_status

Check current deployment status and configuration.

**Parameters:**
- `directory` (string): Project directory to check

**Returns:**
- Project structure status
- Git repository status
- Virtual environment info

### Tool: update_webapp_config

Generate PythonAnywhere web app configuration.

**Parameters:**
- `project_directory` (string): Project directory path
- `domain` (string): Domain name

**Returns:**
- Web app settings
- Static file mappings
- Environment variables
- Configuration JSON

### Tool: run_django_commands

Execute Django management commands.

**Parameters:**
- `commands` (array): Django commands to run
- `directory` (string): Project directory

**Example:**
```json
{
  "commands": ["collectstatic --noinput", "migrate", "createsuperuser"],
  "directory": "/home/<USER>/SMSMali"
}
```

### Tool: check_git_status

Check Git repository status and authentication.

**Parameters:**
- `directory` (string): Repository directory

**Returns:**
- Repository information
- Branch status
- Working tree status
- Remote connectivity

## 🎯 Deployment Workflow

### Complete Deployment Process

1. **Deploy from Git**
   ```bash
   python mcp_deployment_client.py deploy --production
   ```

2. **Check Status**
   ```bash
   python mcp_deployment_client.py status
   ```

3. **Run Django Commands**
   ```bash
   python mcp_deployment_client.py django-commands
   ```

4. **Get Configuration**
   ```bash
   python mcp_deployment_client.py config
   ```

### PythonAnywhere Setup

After deployment, configure your PythonAnywhere web app:

1. **Web App Settings:**
   - Source code: `/home/<USER>/SMSMali`
   - WSGI file: `/home/<USER>/SMSMali/wsgi.py`
   - Virtual environment: `/home/<USER>/SMSMali/venv`

2. **Static Files:**
   - `/static/` → `/home/<USER>/SMSMali/staticfiles`
   - `/media/` → `/home/<USER>/SMSMali/media`

3. **Environment Variables:**
   - `DJANGO_ENV` = `production`
   - `DJANGO_SECRET_KEY` = `your-secret-key`

## 🔧 Troubleshooting

### Common Issues

1. **MCP Import Error**
   ```bash
   pip install mcp
   ```

2. **Permission Denied**
   ```bash
   chmod +x mcp_deployment_server.py
   chmod +x mcp_deployment_client.py
   ```

3. **Git Authentication**
   - Use Personal Access Token for HTTPS
   - Or set up SSH keys

4. **Virtual Environment Issues**
   ```bash
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```

### Debug Mode

Run with debug output:
```bash
# Server with debug
PYTHONPATH=. python mcp_deployment_server.py --debug

# Client with verbose output
python mcp_deployment_client.py deploy --verbose
```

## 📋 Configuration Files

### MCP Server Config (`mcp_server_config.json`)
```json
{
  "mcpServers": {
    "smsmali-deployment": {
      "command": "python",
      "args": ["mcp_deployment_server.py"],
      "cwd": ".",
      "env": {
        "PYTHONPATH": ".",
        "DEPLOYMENT_ENV": "production"
      }
    }
  }
}
```

## 🚀 Advanced Usage

### Custom Deployment Script

Create a custom deployment script:

```python
import asyncio
from mcp_deployment_client import SMSMaliDeploymentClient

async def custom_deploy():
    client = SMSMaliDeploymentClient()
    await client.connect()
    
    # Deploy with custom settings
    await client.deploy_from_git(
        git_url="your-repo-url",
        target_directory="/custom/path",
        production=True,
        force_clean=True
    )
    
    # Check status
    await client.check_status()
    
    # Run custom Django commands
    await client.run_django_commands([
        "collectstatic --noinput",
        "migrate",
        "loaddata initial_data.json"
    ])
    
    await client.close()

# Run custom deployment
asyncio.run(custom_deploy())
```

## 📞 Support

For issues with the MCP deployment server:

1. Check the server logs
2. Verify MCP installation
3. Test Git connectivity
4. Check PythonAnywhere permissions

---

🎉 **Your SMSMali MCP deployment server is ready!**

Use `python mcp_deployment_client.py deploy` to start deploying to PythonAnywhere.
