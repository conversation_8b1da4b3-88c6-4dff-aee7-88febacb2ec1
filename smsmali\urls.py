from django.contrib import admin
from django.urls import path, include, re_path
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import TemplateView
from django.views.generic.base import RedirectView
from django.contrib.staticfiles.storage import staticfiles_storage

urlpatterns = [
    # API routes
    path('admin/', admin.site.urls),
    path('api/products/', include('products.urls')),
    path('api/loans/', include('loans.urls')),
    path('api/users/', include('users.urls')),
    path('api/docs/', TemplateView.as_view(template_name='api_docs.html'), name='api-docs'),

    # Static file routes
    path('robots.txt', RedirectView.as_view(url=staticfiles_storage.url('robots.txt')), name='robots.txt'),
    path('sitemap.xml', RedirectView.as_view(url=staticfiles_storage.url('sitemap.xml')), name='sitemap.xml'),

    # React app - serve index.html for all non-API routes
    re_path(r'^.*$', TemplateView.as_view(template_name='index.html'), name='react-app'),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)