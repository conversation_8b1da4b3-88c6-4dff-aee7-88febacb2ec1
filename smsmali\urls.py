from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import TemplateView
from django.views.generic.base import RedirectView
from django.contrib.staticfiles.storage import staticfiles_storage

urlpatterns = [
    path('', TemplateView.as_view(template_name='home.html'), name='home'),
    path('admin/', admin.site.urls),
    path('api/products/', include('products.urls')),
    path('api/loans/', include('loans.urls')),
    path('api/users/', include('users.urls')),
    path('api/docs/', TemplateView.as_view(template_name='api_docs.html'), name='api-docs'),
    path('robots.txt', RedirectView.as_view(url=staticfiles_storage.url('robots.txt')), name='robots.txt'),
    path('sitemap.xml', RedirectView.as_view(url=staticfiles_storage.url('sitemap.xml')), name='sitemap.xml'),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)