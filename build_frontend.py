#!/usr/bin/env python3
"""
Frontend Build Script for SMSMali
=================================

This script builds the React frontend for production deployment
and prepares it for PythonAnywhere hosting.

Usage:
    python build_frontend.py [--clean] [--dev]

Options:
    --clean    Clean build directories before building
    --dev      Build for development (with source maps)

Author: SMSMali Development Team
"""

import os
import sys
import subprocess
import shutil
import json
from pathlib import Path
import argparse

class FrontendBuilder:
    def __init__(self, clean=False, dev_mode=False):
        self.clean = clean
        self.dev_mode = dev_mode
        self.base_dir = Path(__file__).resolve().parent
        self.frontend_dir = self.base_dir / "frontend"
        self.static_dir = self.base_dir / "static"
        self.dist_dir = self.frontend_dir / "dist"
        self.node_modules_dir = self.frontend_dir / "node_modules"
        
        print("🏗️  SMSMali Frontend Build Script")
        print(f"📁 Frontend Directory: {self.frontend_dir}")
        print(f"🎯 Build Mode: {'Development' if dev_mode else 'Production'}")
        print(f"🧹 Clean Build: {'Yes' if clean else 'No'}")
        print("-" * 50)

    def run_command(self, command, description, cwd=None, check=True):
        """Execute a shell command with error handling"""
        print(f"⚡ {description}...")
        try:
            if isinstance(command, str):
                result = subprocess.run(command, shell=True, cwd=cwd or self.frontend_dir, 
                                      check=check, capture_output=True, text=True)
            else:
                result = subprocess.run(command, cwd=cwd or self.frontend_dir, 
                                      check=check, capture_output=True, text=True)
            
            if result.stdout:
                # Only show last few lines to avoid clutter
                lines = result.stdout.strip().split('\n')
                if len(lines) > 3:
                    print(f"   ... (showing last 3 lines)")
                    for line in lines[-3:]:
                        print(f"   {line}")
                else:
                    for line in lines:
                        print(f"   {line}")
            
            print(f"   ✅ {description} completed")
            return result
            
        except subprocess.CalledProcessError as e:
            print(f"   ❌ Error: {e}")
            if e.stderr:
                print(f"   📝 Details: {e.stderr}")
            if check:
                sys.exit(1)
            return e

    def check_prerequisites(self):
        """Check if Node.js and npm are available"""
        print("🔍 Checking prerequisites...")
        
        # Check if frontend directory exists
        if not self.frontend_dir.exists():
            print(f"   ❌ Frontend directory not found: {self.frontend_dir}")
            sys.exit(1)
        
        # Check package.json
        package_json = self.frontend_dir / "package.json"
        if not package_json.exists():
            print(f"   ❌ package.json not found: {package_json}")
            sys.exit(1)
        
        # Check Node.js
        try:
            result = subprocess.run(['node', '--version'], capture_output=True, text=True)
            node_version = result.stdout.strip()
            print(f"   ✅ Node.js {node_version}")
        except FileNotFoundError:
            print("   ❌ Node.js not found. Please install Node.js first.")
            sys.exit(1)
        
        # Check npm
        try:
            result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
            npm_version = result.stdout.strip()
            print(f"   ✅ npm {npm_version}")
        except FileNotFoundError:
            print("   ❌ npm not found. Please install npm first.")
            sys.exit(1)
        
        return True

    def clean_build_directories(self):
        """Clean build directories"""
        if not self.clean:
            return
        
        print("🧹 Cleaning build directories...")
        
        directories_to_clean = [
            self.dist_dir,
            self.node_modules_dir,
            self.static_dir / "frontend"
        ]
        
        for directory in directories_to_clean:
            if directory.exists():
                print(f"   🗑️  Removing {directory}")
                shutil.rmtree(directory)
            else:
                print(f"   ⏭️  {directory} doesn't exist, skipping")

    def install_dependencies(self):
        """Install npm dependencies"""
        if self.node_modules_dir.exists() and not self.clean:
            print("   ♻️  node_modules exists, checking for updates...")
            self.run_command("npm ci", "Installing dependencies (clean install)")
        else:
            self.run_command("npm install", "Installing dependencies")

    def update_vite_config_for_production(self):
        """Update Vite config for production build"""
        vite_config = self.frontend_dir / "vite.config.js"
        
        if not vite_config.exists():
            print("   ⚠️  vite.config.js not found, creating basic config...")
            
        # Read current config
        try:
            with open(vite_config, 'r') as f:
                config_content = f.read()
        except:
            config_content = ""
        
        # Create production-optimized config
        production_config = '''import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  base: '/static/frontend/',
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: ''' + ('true' if self.dev_mode else 'false') + ''',
    minify: ''' + ('false' if self.dev_mode else 'true') + ''',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          ui: ['framer-motion', 'react-icons'],
          three: ['@react-three/fiber', '@react-three/drei', 'three']
        }
      }
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
      },
    },
  },
  optimizeDeps: {
    exclude: ['hls.js'],
    include: ['@react-three/drei', '@react-three/fiber']
  },
  define: {
    global: 'globalThis',
  }
})'''
        
        # Backup original config
        if vite_config.exists():
            backup_config = self.frontend_dir / "vite.config.js.backup"
            shutil.copy2(vite_config, backup_config)
            print(f"   📋 Backed up original config to {backup_config}")
        
        # Write production config
        with open(vite_config, 'w') as f:
            f.write(production_config)
        
        print("   ✅ Updated Vite config for production")

    def build_react_app(self):
        """Build the React application"""
        build_command = "npm run build"
        
        if self.dev_mode:
            # For development build with source maps
            self.run_command(build_command, "Building React app (development mode)")
        else:
            # For production build
            self.run_command(build_command, "Building React app (production mode)")
        
        # Check if build was successful
        if not self.dist_dir.exists():
            print("   ❌ Build failed - dist directory not created")
            sys.exit(1)
        
        # Show build statistics
        self.show_build_stats()

    def show_build_stats(self):
        """Show build statistics"""
        print("\n📊 Build Statistics:")
        
        if not self.dist_dir.exists():
            print("   ❌ No build output found")
            return
        
        # Calculate total size
        total_size = 0
        file_count = 0
        
        for file_path in self.dist_dir.rglob('*'):
            if file_path.is_file():
                file_count += 1
                total_size += file_path.stat().st_size
        
        # Convert bytes to human readable
        def human_readable_size(size_bytes):
            for unit in ['B', 'KB', 'MB', 'GB']:
                if size_bytes < 1024:
                    return f"{size_bytes:.1f} {unit}"
                size_bytes /= 1024
            return f"{size_bytes:.1f} TB"
        
        print(f"   📁 Total files: {file_count}")
        print(f"   📏 Total size: {human_readable_size(total_size)}")
        
        # Show main files
        main_files = ['index.html', 'assets']
        for item in main_files:
            item_path = self.dist_dir / item
            if item_path.exists():
                if item_path.is_file():
                    size = human_readable_size(item_path.stat().st_size)
                    print(f"   📄 {item}: {size}")
                else:
                    # Count files in directory
                    dir_files = len(list(item_path.rglob('*')))
                    print(f"   📁 {item}/: {dir_files} files")

    def copy_to_django_static(self):
        """Copy built files to Django static directory"""
        print("📋 Copying build to Django static directory...")
        
        # Create static/frontend directory
        frontend_static_dir = self.static_dir / "frontend"
        
        # Remove existing frontend static files
        if frontend_static_dir.exists():
            shutil.rmtree(frontend_static_dir)
        
        # Create directory
        frontend_static_dir.mkdir(parents=True, exist_ok=True)
        
        # Copy all files from dist to static/frontend
        if self.dist_dir.exists():
            for item in self.dist_dir.iterdir():
                if item.is_file():
                    shutil.copy2(item, frontend_static_dir)
                    print(f"   📄 Copied {item.name}")
                elif item.is_dir():
                    dest_dir = frontend_static_dir / item.name
                    shutil.copytree(item, dest_dir)
                    print(f"   📁 Copied {item.name}/")
        
        print(f"   ✅ Frontend build copied to {frontend_static_dir}")

    def create_build_info(self):
        """Create build information file"""
        from datetime import datetime
        
        build_info = {
            "build_time": datetime.now().isoformat(),
            "build_mode": "development" if self.dev_mode else "production",
            "node_version": subprocess.run(['node', '--version'], capture_output=True, text=True).stdout.strip(),
            "npm_version": subprocess.run(['npm', '--version'], capture_output=True, text=True).stdout.strip(),
            "clean_build": self.clean
        }
        
        # Add package.json info
        package_json = self.frontend_dir / "package.json"
        if package_json.exists():
            with open(package_json, 'r') as f:
                package_data = json.load(f)
                build_info["app_name"] = package_data.get("name", "unknown")
                build_info["app_version"] = package_data.get("version", "unknown")
        
        # Save build info
        build_info_file = self.static_dir / "frontend" / "build-info.json"
        build_info_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(build_info_file, 'w') as f:
            json.dump(build_info, f, indent=2)
        
        print(f"   ✅ Build info saved to {build_info_file}")

    def build(self):
        """Main build process"""
        print("🚀 Starting frontend build process...")
        
        # Check prerequisites
        self.check_prerequisites()
        
        # Clean if requested
        self.clean_build_directories()
        
        # Update Vite config for production
        self.update_vite_config_for_production()
        
        # Install dependencies
        self.install_dependencies()
        
        # Build React app
        self.build_react_app()
        
        # Copy to Django static directory
        self.copy_to_django_static()
        
        # Create build info
        self.create_build_info()
        
        print("\n🎉 Frontend build completed successfully!")
        print(f"📁 Build output: {self.dist_dir}")
        print(f"📁 Django static: {self.static_dir / 'frontend'}")
        print("\n📋 Next steps:")
        print("1. Run Django collectstatic: python manage.py collectstatic")
        print("2. Deploy to PythonAnywhere: python deploy_to_pythonanywhere.py --production")
        print("3. Configure web app on PythonAnywhere")

def main():
    parser = argparse.ArgumentParser(description='Build SMSMali React frontend')
    parser.add_argument('--clean', action='store_true', 
                       help='Clean build directories before building')
    parser.add_argument('--dev', action='store_true',
                       help='Build for development (with source maps)')
    
    args = parser.parse_args()
    
    builder = FrontendBuilder(clean=args.clean, dev_mode=args.dev)
    builder.build()

if __name__ == "__main__":
    main()
