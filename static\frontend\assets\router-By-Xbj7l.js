import{r as ve,g as ge,a as ye}from"./vendor-DWLLDKvm.js";function xe(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const a in r)if(a!=="default"&&!(a in e)){const i=Object.getOwnPropertyDescriptor(r,a);i&&Object.defineProperty(e,a,i.get?i:{enumerable:!0,get:()=>r[a]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var s=ve();const Ce=ge(s),we=xe({__proto__:null,default:Ce},[s]);ye();/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function B(){return B=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},B.apply(this,arguments)}var R;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(R||(R={}));const G="popstate";function Ee(e){e===void 0&&(e={});function t(r,a){let{pathname:i,search:l,hash:u}=r.location;return A("",{pathname:i,search:l,hash:u},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function n(r,a){return typeof a=="string"?a:j(a)}return Re(t,n,null,e)}function x(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function te(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Pe(){return Math.random().toString(36).substr(2,8)}function X(e,t){return{usr:e.state,key:e.key,idx:t}}function A(e,t,n,r){return n===void 0&&(n=null),B({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?U(t):t,{state:n,key:t&&t.key||r||Pe()})}function j(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function U(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function Re(e,t,n,r){r===void 0&&(r={});let{window:a=document.defaultView,v5Compat:i=!1}=r,l=a.history,u=R.Pop,o=null,f=h();f==null&&(f=0,l.replaceState(B({},l.state,{idx:f}),""));function h(){return(l.state||{idx:null}).idx}function c(){u=R.Pop;let d=h(),C=d==null?null:d-f;f=d,o&&o({action:u,location:m.location,delta:C})}function p(d,C){u=R.Push;let v=A(m.location,d,C);f=h()+1;let y=X(v,f),w=m.createHref(v);try{l.pushState(y,"",w)}catch(P){if(P instanceof DOMException&&P.name==="DataCloneError")throw P;a.location.assign(w)}i&&o&&o({action:u,location:m.location,delta:1})}function E(d,C){u=R.Replace;let v=A(m.location,d,C);f=h();let y=X(v,f),w=m.createHref(v);l.replaceState(y,"",w),i&&o&&o({action:u,location:m.location,delta:0})}function g(d){let C=a.location.origin!=="null"?a.location.origin:a.location.href,v=typeof d=="string"?d:j(d);return v=v.replace(/ $/,"%20"),x(C,"No window.location.(origin|href) available to create URL for href: "+v),new URL(v,C)}let m={get action(){return u},get location(){return e(a,l)},listen(d){if(o)throw new Error("A history only accepts one active listener");return a.addEventListener(G,c),o=d,()=>{a.removeEventListener(G,c),o=null}},createHref(d){return t(a,d)},createURL:g,encodeLocation(d){let C=g(d);return{pathname:C.pathname,search:C.search,hash:C.hash}},push:p,replace:E,go(d){return l.go(d)}};return m}var H;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(H||(H={}));function be(e,t,n){return n===void 0&&(n="/"),Se(e,t,n)}function Se(e,t,n,r){let a=typeof t=="string"?U(t):t,i=O(a.pathname||"/",n);if(i==null)return null;let l=ne(e);Le(l);let u=null;for(let o=0;u==null&&o<l.length;++o){let f=$e(i);u=_e(l[o],f)}return u}function ne(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let a=(i,l,u)=>{let o={relativePath:u===void 0?i.path||"":u,caseSensitive:i.caseSensitive===!0,childrenIndex:l,route:i};o.relativePath.startsWith("/")&&(x(o.relativePath.startsWith(r),'Absolute route path "'+o.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),o.relativePath=o.relativePath.slice(r.length));let f=b([r,o.relativePath]),h=n.concat(o);i.children&&i.children.length>0&&(x(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+f+'".')),ne(i.children,t,h,f)),!(i.path==null&&!i.index)&&t.push({path:f,score:je(f,i.index),routesMeta:h})};return e.forEach((i,l)=>{var u;if(i.path===""||!((u=i.path)!=null&&u.includes("?")))a(i,l);else for(let o of re(i.path))a(i,l,o)}),t}function re(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,a=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return a?[i,""]:[i];let l=re(r.join("/")),u=[];return u.push(...l.map(o=>o===""?i:[i,o].join("/"))),a&&u.push(...l),u.map(o=>e.startsWith("/")&&o===""?"/":o)}function Le(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:ke(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const Oe=/^:[\w-]+$/,Ue=3,Be=2,Ne=1,Te=10,Ie=-2,Q=e=>e==="*";function je(e,t){let n=e.split("/"),r=n.length;return n.some(Q)&&(r+=Ie),t&&(r+=Be),n.filter(a=>!Q(a)).reduce((a,i)=>a+(Oe.test(i)?Ue:i===""?Ne:Te),r)}function ke(e,t){return e.length===t.length&&e.slice(0,-1).every((r,a)=>r===t[a])?e[e.length-1]-t[t.length-1]:0}function _e(e,t,n){let{routesMeta:r}=e,a={},i="/",l=[];for(let u=0;u<r.length;++u){let o=r[u],f=u===r.length-1,h=i==="/"?t:t.slice(i.length)||"/",c=z({path:o.relativePath,caseSensitive:o.caseSensitive,end:f},h),p=o.route;if(!c)return null;Object.assign(a,c.params),l.push({params:a,pathname:b([i,c.pathname]),pathnameBase:De(b([i,c.pathnameBase])),route:p}),c.pathnameBase!=="/"&&(i=b([i,c.pathnameBase]))}return l}function z(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=We(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let i=a[0],l=i.replace(/(.)\/+$/,"$1"),u=a.slice(1);return{params:r.reduce((f,h,c)=>{let{paramName:p,isOptional:E}=h;if(p==="*"){let m=u[c]||"";l=i.slice(0,i.length-m.length).replace(/(.)\/+$/,"$1")}const g=u[c];return E&&!g?f[p]=void 0:f[p]=(g||"").replace(/%2F/g,"/"),f},{}),pathname:i,pathnameBase:l,pattern:e}}function We(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),te(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(l,u,o)=>(r.push({paramName:u,isOptional:o!=null}),o?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function $e(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return te(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function O(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function Fe(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:a=""}=typeof e=="string"?U(e):e;return{pathname:n?n.startsWith("/")?n:Me(n,t):t,search:Ae(r),hash:ze(a)}}function Me(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?n.length>1&&n.pop():a!=="."&&n.push(a)}),n.length>1?n.join("/"):"/"}function D(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Ve(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function ae(e,t){let n=Ve(e);return t?n.map((r,a)=>a===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function ie(e,t,n,r){r===void 0&&(r=!1);let a;typeof e=="string"?a=U(e):(a=B({},e),x(!a.pathname||!a.pathname.includes("?"),D("?","pathname","search",a)),x(!a.pathname||!a.pathname.includes("#"),D("#","pathname","hash",a)),x(!a.search||!a.search.includes("#"),D("#","search","hash",a)));let i=e===""||a.pathname==="",l=i?"/":a.pathname,u;if(l==null)u=n;else{let c=t.length-1;if(!r&&l.startsWith("..")){let p=l.split("/");for(;p[0]==="..";)p.shift(),c-=1;a.pathname=p.join("/")}u=c>=0?t[c]:"/"}let o=Fe(a,u),f=l&&l!=="/"&&l.endsWith("/"),h=(i||l===".")&&n.endsWith("/");return!o.pathname.endsWith("/")&&(f||h)&&(o.pathname+="/"),o}const b=e=>e.join("/").replace(/\/\/+/g,"/"),De=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Ae=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,ze=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Je(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const le=["post","put","patch","delete"];new Set(le);const Ke=["get",...le];new Set(Ke);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function N(){return N=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},N.apply(this,arguments)}const _=s.createContext(null),oe=s.createContext(null),S=s.createContext(null),W=s.createContext(null),L=s.createContext({outlet:null,matches:[],isDataRoute:!1}),se=s.createContext(null);function qe(e,t){let{relative:n}=t===void 0?{}:t;T()||x(!1);let{basename:r,navigator:a}=s.useContext(S),{hash:i,pathname:l,search:u}=$(e,{relative:n}),o=l;return r!=="/"&&(o=l==="/"?r:b([r,l])),a.createHref({pathname:o,search:u,hash:i})}function T(){return s.useContext(W)!=null}function I(){return T()||x(!1),s.useContext(W).location}function ue(e){s.useContext(S).static||s.useLayoutEffect(e)}function Ge(){let{isDataRoute:e}=s.useContext(L);return e?ot():Xe()}function Xe(){T()||x(!1);let e=s.useContext(_),{basename:t,future:n,navigator:r}=s.useContext(S),{matches:a}=s.useContext(L),{pathname:i}=I(),l=JSON.stringify(ae(a,n.v7_relativeSplatPath)),u=s.useRef(!1);return ue(()=>{u.current=!0}),s.useCallback(function(f,h){if(h===void 0&&(h={}),!u.current)return;if(typeof f=="number"){r.go(f);return}let c=ie(f,JSON.parse(l),i,h.relative==="path");e==null&&t!=="/"&&(c.pathname=c.pathname==="/"?t:b([t,c.pathname])),(h.replace?r.replace:r.push)(c,h.state,h)},[t,r,l,i,e])}function St(){let{matches:e}=s.useContext(L),t=e[e.length-1];return t?t.params:{}}function $(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=s.useContext(S),{matches:a}=s.useContext(L),{pathname:i}=I(),l=JSON.stringify(ae(a,r.v7_relativeSplatPath));return s.useMemo(()=>ie(e,JSON.parse(l),i,n==="path"),[e,l,i,n])}function He(e,t){return Qe(e,t)}function Qe(e,t,n,r){T()||x(!1);let{navigator:a}=s.useContext(S),{matches:i}=s.useContext(L),l=i[i.length-1],u=l?l.params:{};l&&l.pathname;let o=l?l.pathnameBase:"/";l&&l.route;let f=I(),h;if(t){var c;let d=typeof t=="string"?U(t):t;o==="/"||(c=d.pathname)!=null&&c.startsWith(o)||x(!1),h=d}else h=f;let p=h.pathname||"/",E=p;if(o!=="/"){let d=o.replace(/^\//,"").split("/");E="/"+p.replace(/^\//,"").split("/").slice(d.length).join("/")}let g=be(e,{pathname:E}),m=nt(g&&g.map(d=>Object.assign({},d,{params:Object.assign({},u,d.params),pathname:b([o,a.encodeLocation?a.encodeLocation(d.pathname).pathname:d.pathname]),pathnameBase:d.pathnameBase==="/"?o:b([o,a.encodeLocation?a.encodeLocation(d.pathnameBase).pathname:d.pathnameBase])})),i,n,r);return t&&m?s.createElement(W.Provider,{value:{location:N({pathname:"/",search:"",hash:"",state:null,key:"default"},h),navigationType:R.Pop}},m):m}function Ye(){let e=lt(),t=Je(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return s.createElement(s.Fragment,null,s.createElement("h2",null,"Unexpected Application Error!"),s.createElement("h3",{style:{fontStyle:"italic"}},t),n?s.createElement("pre",{style:a},n):null,null)}const Ze=s.createElement(Ye,null);class et extends s.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?s.createElement(L.Provider,{value:this.props.routeContext},s.createElement(se.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function tt(e){let{routeContext:t,match:n,children:r}=e,a=s.useContext(_);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),s.createElement(L.Provider,{value:t},r)}function nt(e,t,n,r){var a;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var i;if(!n)return null;if(n.errors)e=n.matches;else if((i=r)!=null&&i.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let l=e,u=(a=n)==null?void 0:a.errors;if(u!=null){let h=l.findIndex(c=>c.route.id&&u?.[c.route.id]!==void 0);h>=0||x(!1),l=l.slice(0,Math.min(l.length,h+1))}let o=!1,f=-1;if(n&&r&&r.v7_partialHydration)for(let h=0;h<l.length;h++){let c=l[h];if((c.route.HydrateFallback||c.route.hydrateFallbackElement)&&(f=h),c.route.id){let{loaderData:p,errors:E}=n,g=c.route.loader&&p[c.route.id]===void 0&&(!E||E[c.route.id]===void 0);if(c.route.lazy||g){o=!0,f>=0?l=l.slice(0,f+1):l=[l[0]];break}}}return l.reduceRight((h,c,p)=>{let E,g=!1,m=null,d=null;n&&(E=u&&c.route.id?u[c.route.id]:void 0,m=c.route.errorElement||Ze,o&&(f<0&&p===0?(st("route-fallback"),g=!0,d=null):f===p&&(g=!0,d=c.route.hydrateFallbackElement||null)));let C=t.concat(l.slice(0,p+1)),v=()=>{let y;return E?y=m:g?y=d:c.route.Component?y=s.createElement(c.route.Component,null):c.route.element?y=c.route.element:y=h,s.createElement(tt,{match:c,routeContext:{outlet:h,matches:C,isDataRoute:n!=null},children:y})};return n&&(c.route.ErrorBoundary||c.route.errorElement||p===0)?s.createElement(et,{location:n.location,revalidation:n.revalidation,component:m,error:E,children:v(),routeContext:{outlet:null,matches:C,isDataRoute:!0}}):v()},null)}var ce=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(ce||{}),fe=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(fe||{});function rt(e){let t=s.useContext(_);return t||x(!1),t}function at(e){let t=s.useContext(oe);return t||x(!1),t}function it(e){let t=s.useContext(L);return t||x(!1),t}function he(e){let t=it(),n=t.matches[t.matches.length-1];return n.route.id||x(!1),n.route.id}function lt(){var e;let t=s.useContext(se),n=at(),r=he();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function ot(){let{router:e}=rt(ce.UseNavigateStable),t=he(fe.UseNavigateStable),n=s.useRef(!1);return ue(()=>{n.current=!0}),s.useCallback(function(a,i){i===void 0&&(i={}),n.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,N({fromRouteId:t},i)))},[e,t])}const Y={};function st(e,t,n){Y[e]||(Y[e]=!0)}function ut(e,t){e?.v7_startTransition,e?.v7_relativeSplatPath}function ct(e){x(!1)}function ft(e){let{basename:t="/",children:n=null,location:r,navigationType:a=R.Pop,navigator:i,static:l=!1,future:u}=e;T()&&x(!1);let o=t.replace(/^\/*/,"/"),f=s.useMemo(()=>({basename:o,navigator:i,static:l,future:N({v7_relativeSplatPath:!1},u)}),[o,u,i,l]);typeof r=="string"&&(r=U(r));let{pathname:h="/",search:c="",hash:p="",state:E=null,key:g="default"}=r,m=s.useMemo(()=>{let d=O(h,o);return d==null?null:{location:{pathname:d,search:c,hash:p,state:E,key:g},navigationType:a}},[o,h,c,p,E,g,a]);return m==null?null:s.createElement(S.Provider,{value:f},s.createElement(W.Provider,{children:n,value:m}))}function Lt(e){let{children:t,location:n}=e;return He(J(t),n)}new Promise(()=>{});function J(e,t){t===void 0&&(t=[]);let n=[];return s.Children.forEach(e,(r,a)=>{if(!s.isValidElement(r))return;let i=[...t,a];if(r.type===s.Fragment){n.push.apply(n,J(r.props.children,i));return}r.type!==ct&&x(!1),!r.props.index||!r.props.children||x(!1);let l={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(l.children=J(r.props.children,i)),n.push(l)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function k(){return k=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},k.apply(this,arguments)}function de(e,t){if(e==null)return{};var n={},r=Object.keys(e),a,i;for(i=0;i<r.length;i++)a=r[i],!(t.indexOf(a)>=0)&&(n[a]=e[a]);return n}function ht(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function dt(e,t){return e.button===0&&(!t||t==="_self")&&!ht(e)}const pt=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],mt=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],vt="6";try{window.__reactRouterVersion=vt}catch{}const gt=s.createContext({isTransitioning:!1}),yt="startTransition",Z=we[yt];function Ot(e){let{basename:t,children:n,future:r,window:a}=e,i=s.useRef();i.current==null&&(i.current=Ee({window:a,v5Compat:!0}));let l=i.current,[u,o]=s.useState({action:l.action,location:l.location}),{v7_startTransition:f}=r||{},h=s.useCallback(c=>{f&&Z?Z(()=>o(c)):o(c)},[o,f]);return s.useLayoutEffect(()=>l.listen(h),[l,h]),s.useEffect(()=>ut(r),[r]),s.createElement(ft,{basename:t,children:n,location:u.location,navigationType:u.action,navigator:l,future:r})}const xt=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Ct=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,wt=s.forwardRef(function(t,n){let{onClick:r,relative:a,reloadDocument:i,replace:l,state:u,target:o,to:f,preventScrollReset:h,viewTransition:c}=t,p=de(t,pt),{basename:E}=s.useContext(S),g,m=!1;if(typeof f=="string"&&Ct.test(f)&&(g=f,xt))try{let y=new URL(window.location.href),w=f.startsWith("//")?new URL(y.protocol+f):new URL(f),P=O(w.pathname,E);w.origin===y.origin&&P!=null?f=P+w.search+w.hash:m=!0}catch{}let d=qe(f,{relative:a}),C=Pt(f,{replace:l,state:u,target:o,preventScrollReset:h,relative:a,viewTransition:c});function v(y){r&&r(y),y.defaultPrevented||C(y)}return s.createElement("a",k({},p,{href:g||d,onClick:m||i?r:v,ref:n,target:o}))}),Ut=s.forwardRef(function(t,n){let{"aria-current":r="page",caseSensitive:a=!1,className:i="",end:l=!1,style:u,to:o,viewTransition:f,children:h}=t,c=de(t,mt),p=$(o,{relative:c.relative}),E=I(),g=s.useContext(oe),{navigator:m,basename:d}=s.useContext(S),C=g!=null&&Rt(p)&&f===!0,v=m.encodeLocation?m.encodeLocation(p).pathname:p.pathname,y=E.pathname,w=g&&g.navigation&&g.navigation.location?g.navigation.location.pathname:null;a||(y=y.toLowerCase(),w=w?w.toLowerCase():null,v=v.toLowerCase()),w&&d&&(w=O(w,d)||w);const P=v!=="/"&&v.endsWith("/")?v.length-1:v.length;let F=y===v||!l&&y.startsWith(v)&&y.charAt(P)==="/",q=w!=null&&(w===v||!l&&w.startsWith(v)&&w.charAt(v.length)==="/"),M={isActive:F,isPending:q,isTransitioning:C},pe=F?r:void 0,V;typeof i=="function"?V=i(M):V=[i,F?"active":null,q?"pending":null,C?"transitioning":null].filter(Boolean).join(" ");let me=typeof u=="function"?u(M):u;return s.createElement(wt,k({},c,{"aria-current":pe,className:V,ref:n,style:me,to:o,viewTransition:f}),typeof h=="function"?h(M):h)});var K;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(K||(K={}));var ee;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(ee||(ee={}));function Et(e){let t=s.useContext(_);return t||x(!1),t}function Pt(e,t){let{target:n,replace:r,state:a,preventScrollReset:i,relative:l,viewTransition:u}=t===void 0?{}:t,o=Ge(),f=I(),h=$(e,{relative:l});return s.useCallback(c=>{if(dt(c,n)){c.preventDefault();let p=r!==void 0?r:j(f)===j(h);o(e,{replace:p,state:a,preventScrollReset:i,relative:l,viewTransition:u})}},[f,o,h,r,a,n,e,i,l,u])}function Rt(e,t){t===void 0&&(t={});let n=s.useContext(gt);n==null&&x(!1);let{basename:r}=Et(K.useViewTransitionState),a=$(e,{relative:t.relative});if(!n.isTransitioning)return!1;let i=O(n.currentLocation.pathname,r)||n.currentLocation.pathname,l=O(n.nextLocation.pathname,r)||n.nextLocation.pathname;return z(a.pathname,l)!=null||z(a.pathname,i)!=null}export{Ot as B,wt as L,Ut as N,Ce as R,Lt as a,ct as b,s as r,St as u};
