import{R as W,r as P}from"./router-By-Xbj7l.js";var as={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},Qe=W.createContext&&W.createContext(as),X=function(){return X=Object.assign||function(t){for(var e,n=1,s=arguments.length;n<s;n++){e=arguments[n];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])}return t},X.apply(this,arguments)},Li=function(t,e){var n={};for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&e.indexOf(s)<0&&(n[s]=t[s]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,s=Object.getOwnPropertySymbols(t);i<s.length;i++)e.indexOf(s[i])<0&&Object.prototype.propertyIsEnumerable.call(t,s[i])&&(n[s[i]]=t[s[i]]);return n};function ls(t){return t&&t.map(function(e,n){return W.createElement(e.tag,X({key:n},e.attr),ls(e.child))})}function Hl(t){return function(e){return W.createElement(Fi,X({attr:X({},t.attr)},e),ls(t.child))}}function Fi(t){var e=function(n){var s=t.attr,i=t.size,o=t.title,r=Li(t,["attr","size","title"]),a=i||n.size||"1em",l;return n.className&&(l=n.className),t.className&&(l=(l?l+" ":"")+t.className),W.createElement("svg",X({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},n.attr,s,r,{className:l,style:X(X({color:t.color||n.color},n.style),t.style),height:a,width:a,xmlns:"http://www.w3.org/2000/svg"}),o&&W.createElement("title",null,o),t.children)};return Qe!==void 0?W.createElement(Qe.Consumer,null,function(n){return e(n)}):e(as)}const cs=P.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),Wt=P.createContext({}),Se=P.createContext(null),Ve=typeof document<"u",Bi=Ve?P.useLayoutEffect:P.useEffect,us=P.createContext({strict:!1}),we=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),ki="framerAppearId",hs="data-"+we(ki),Oi={useManualTiming:!1};class Je{constructor(){this.order=[],this.scheduled=new Set}add(e){if(!this.scheduled.has(e))return this.scheduled.add(e),this.order.push(e),!0}remove(e){const n=this.order.indexOf(e);n!==-1&&(this.order.splice(n,1),this.scheduled.delete(e))}clear(){this.order.length=0,this.scheduled.clear()}}function ji(t){let e=new Je,n=new Je,s=0,i=!1,o=!1;const r=new WeakSet,a={schedule:(l,c=!1,u=!1)=>{const h=u&&i,f=h?e:n;return c&&r.add(l),f.add(l)&&h&&i&&(s=e.order.length),l},cancel:l=>{n.remove(l),r.delete(l)},process:l=>{if(i){o=!0;return}if(i=!0,[e,n]=[n,e],n.clear(),s=e.order.length,s)for(let c=0;c<s;c++){const u=e.order[c];r.has(u)&&(a.schedule(u),t()),u(l)}i=!1,o&&(o=!1,a.process(l))}};return a}const Mt=["read","resolveKeyframes","update","preRender","render","postRender"],Ii=40;function fs(t,e){let n=!1,s=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=Mt.reduce((h,f)=>(h[f]=ji(()=>n=!0),h),{}),r=h=>{o[h].process(i)},a=()=>{const h=performance.now();n=!1,i.delta=s?1e3/60:Math.max(Math.min(h-i.timestamp,Ii),1),i.timestamp=h,i.isProcessing=!0,Mt.forEach(r),i.isProcessing=!1,n&&e&&(s=!1,t(a))},l=()=>{n=!0,s=!0,i.isProcessing||t(a)};return{schedule:Mt.reduce((h,f)=>{const d=o[f];return h[f]=(m,p=!1,y=!1)=>(n||l(),d.schedule(m,p,y)),h},{}),cancel:h=>Mt.forEach(f=>o[f].cancel(h)),state:i,steps:o}}const{schedule:Ae}=fs(queueMicrotask,!1);function Ni(t,e,n,s){const{visualElement:i}=P.useContext(Wt),o=P.useContext(us),r=P.useContext(Se),a=P.useContext(cs).reducedMotion,l=P.useRef();s=s||o.renderer,!l.current&&s&&(l.current=s(t,{visualState:e,parent:i,props:n,presenceContext:r,blockInitialAnimation:r?r.initial===!1:!1,reducedMotionConfig:a}));const c=l.current;P.useInsertionEffect(()=>{c&&c.update(n,r)});const u=P.useRef(!!(n[hs]&&!window.HandoffComplete));return Bi(()=>{c&&(Ae.postRender(c.render),u.current&&c.animationState&&c.animationState.animateChanges())}),P.useEffect(()=>{c&&(c.updateFeatures(),!u.current&&c.animationState&&c.animationState.animateChanges(),u.current&&(u.current=!1,window.HandoffComplete=!0))}),c}function ct(t){return t&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function Ui(t,e,n){return P.useCallback(s=>{s&&t.mount&&t.mount(s),e&&(s?e.mount(s):e.unmount()),n&&(typeof n=="function"?n(s):ct(n)&&(n.current=s))},[e])}function Pt(t){return typeof t=="string"||Array.isArray(t)}function _t(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}const Ce=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],De=["initial",...Ce];function Gt(t){return _t(t.animate)||De.some(e=>Pt(t[e]))}function ds(t){return!!(Gt(t)||t.variants)}function Ki(t,e){if(Gt(t)){const{initial:n,animate:s}=t;return{initial:n===!1||Pt(n)?n:void 0,animate:Pt(s)?s:void 0}}return t.inherit!==!1?e:{}}function Wi(t){const{initial:e,animate:n}=Ki(t,P.useContext(Wt));return P.useMemo(()=>({initial:e,animate:n}),[tn(e),tn(n)])}function tn(t){return Array.isArray(t)?t.join(" "):t}const en={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},bt={};for(const t in en)bt[t]={isEnabled:e=>en[t].some(n=>!!e[n])};function _i(t){for(const e in t)bt[e]={...bt[e],...t[e]}}const ms=P.createContext({}),ps=P.createContext({}),Gi=Symbol.for("motionComponentSymbol");function $i({preloadedFeatures:t,createVisualElement:e,useRender:n,useVisualState:s,Component:i}){t&&_i(t);function o(a,l){let c;const u={...P.useContext(cs),...a,layoutId:zi(a)},{isStatic:h}=u,f=Wi(a),d=s(a,h);if(!h&&Ve){f.visualElement=Ni(i,d,u,e);const m=P.useContext(ps),p=P.useContext(us).strict;f.visualElement&&(c=f.visualElement.loadFeatures(u,p,t,m))}return P.createElement(Wt.Provider,{value:f},c&&f.visualElement?P.createElement(c,{visualElement:f.visualElement,...u}):null,n(i,a,Ui(d,f.visualElement,l),d,h,f.visualElement))}const r=P.forwardRef(o);return r[Gi]=i,r}function zi({layoutId:t}){const e=P.useContext(ms).id;return e&&t!==void 0?e+"-"+t:t}function Hi(t){function e(s,i={}){return $i(t(s,i))}if(typeof Proxy>"u")return e;const n=new Map;return new Proxy(e,{get:(s,i)=>(n.has(i)||n.set(i,e(i)),n.get(i))})}const Yi=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Me(t){return typeof t!="string"||t.includes("-")?!1:!!(Yi.indexOf(t)>-1||/[A-Z]/u.test(t))}const kt={};function Xi(t){Object.assign(kt,t)}const wt=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],at=new Set(wt);function gs(t,{layout:e,layoutId:n}){return at.has(t)||t.startsWith("origin")||(e||n!==void 0)&&(!!kt[t]||t==="opacity")}const E=t=>!!(t&&t.getVelocity),qi={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Zi=wt.length;function Qi(t,{enableHardwareAcceleration:e=!0,allowTransformNone:n=!0},s,i){let o="";for(let r=0;r<Zi;r++){const a=wt[r];if(t[a]!==void 0){const l=qi[a]||a;o+=`${l}(${t[a]}) `}}return e&&!t.z&&(o+="translateZ(0)"),o=o.trim(),i?o=i(t,s?"":o):n&&s&&(o="none"),o}const ys=t=>e=>typeof e=="string"&&e.startsWith(t),vs=ys("--"),Ji=ys("var(--"),Re=t=>Ji(t)?tr.test(t.split("/*")[0].trim()):!1,tr=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,er=(t,e)=>e&&typeof t=="number"?e.transform(t):t,Q=(t,e,n)=>n>e?e:n<t?t:n,mt={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},yt={...mt,transform:t=>Q(0,1,t)},Rt={...mt,default:1},vt=t=>Math.round(t*1e5)/1e5,Ee=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,nr=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,sr=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu;function At(t){return typeof t=="string"}const Ct=t=>({test:e=>At(e)&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),H=Ct("deg"),N=Ct("%"),x=Ct("px"),ir=Ct("vh"),rr=Ct("vw"),nn={...N,parse:t=>N.parse(t)/100,transform:t=>N.transform(t*100)},sn={...mt,transform:Math.round},xs={borderWidth:x,borderTopWidth:x,borderRightWidth:x,borderBottomWidth:x,borderLeftWidth:x,borderRadius:x,radius:x,borderTopLeftRadius:x,borderTopRightRadius:x,borderBottomRightRadius:x,borderBottomLeftRadius:x,width:x,maxWidth:x,height:x,maxHeight:x,size:x,top:x,right:x,bottom:x,left:x,padding:x,paddingTop:x,paddingRight:x,paddingBottom:x,paddingLeft:x,margin:x,marginTop:x,marginRight:x,marginBottom:x,marginLeft:x,rotate:H,rotateX:H,rotateY:H,rotateZ:H,scale:Rt,scaleX:Rt,scaleY:Rt,scaleZ:Rt,skew:H,skewX:H,skewY:H,distance:x,translateX:x,translateY:x,translateZ:x,x,y:x,z:x,perspective:x,transformPerspective:x,opacity:yt,originX:nn,originY:nn,originZ:x,zIndex:sn,backgroundPositionX:x,backgroundPositionY:x,fillOpacity:yt,strokeOpacity:yt,numOctaves:sn};function Le(t,e,n,s){const{style:i,vars:o,transform:r,transformOrigin:a}=t;let l=!1,c=!1,u=!0;for(const h in e){const f=e[h];if(vs(h)){o[h]=f;continue}const d=xs[h],m=er(f,d);if(at.has(h)){if(l=!0,r[h]=m,!u)continue;f!==(d.default||0)&&(u=!1)}else h.startsWith("origin")?(c=!0,a[h]=m):i[h]=m}if(e.transform||(l||s?i.transform=Qi(t.transform,n,u,s):i.transform&&(i.transform="none")),c){const{originX:h="50%",originY:f="50%",originZ:d=0}=a;i.transformOrigin=`${h} ${f} ${d}`}}const Fe=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Ts(t,e,n){for(const s in e)!E(e[s])&&!gs(s,n)&&(t[s]=e[s])}function or({transformTemplate:t},e,n){return P.useMemo(()=>{const s=Fe();return Le(s,e,{enableHardwareAcceleration:!n},t),Object.assign({},s.vars,s.style)},[e])}function ar(t,e,n){const s=t.style||{},i={};return Ts(i,s,t),Object.assign(i,or(t,e,n)),i}function lr(t,e,n){const s={},i=ar(t,e,n);return t.drag&&t.dragListener!==!1&&(s.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),t.tabIndex===void 0&&(t.onTap||t.onTapStart||t.whileTap)&&(s.tabIndex=0),s.style=i,s}const cr=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Ot(t){return t.startsWith("while")||t.startsWith("drag")&&t!=="draggable"||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||cr.has(t)}let Ps=t=>!Ot(t);function ur(t){t&&(Ps=e=>e.startsWith("on")?!Ot(e):t(e))}try{ur(require("@emotion/is-prop-valid").default)}catch{}function hr(t,e,n){const s={};for(const i in t)i==="values"&&typeof t.values=="object"||(Ps(i)||n===!0&&Ot(i)||!e&&!Ot(i)||t.draggable&&i.startsWith("onDrag"))&&(s[i]=t[i]);return s}function rn(t,e,n){return typeof t=="string"?t:x.transform(e+n*t)}function fr(t,e,n){const s=rn(e,t.x,t.width),i=rn(n,t.y,t.height);return`${s} ${i}`}const dr={offset:"stroke-dashoffset",array:"stroke-dasharray"},mr={offset:"strokeDashoffset",array:"strokeDasharray"};function pr(t,e,n=1,s=0,i=!0){t.pathLength=1;const o=i?dr:mr;t[o.offset]=x.transform(-s);const r=x.transform(e),a=x.transform(n);t[o.array]=`${r} ${a}`}function Be(t,{attrX:e,attrY:n,attrScale:s,originX:i,originY:o,pathLength:r,pathSpacing:a=1,pathOffset:l=0,...c},u,h,f){if(Le(t,c,u,f),h){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:d,style:m,dimensions:p}=t;d.transform&&(p&&(m.transform=d.transform),delete d.transform),p&&(i!==void 0||o!==void 0||m.transform)&&(m.transformOrigin=fr(p,i!==void 0?i:.5,o!==void 0?o:.5)),e!==void 0&&(d.x=e),n!==void 0&&(d.y=n),s!==void 0&&(d.scale=s),r!==void 0&&pr(d,r,a,l,!1)}const bs=()=>({...Fe(),attrs:{}}),ke=t=>typeof t=="string"&&t.toLowerCase()==="svg";function gr(t,e,n,s){const i=P.useMemo(()=>{const o=bs();return Be(o,e,{enableHardwareAcceleration:!1},ke(s),t.transformTemplate),{...o.attrs,style:{...o.style}}},[e]);if(t.style){const o={};Ts(o,t.style,t),i.style={...o,...i.style}}return i}function yr(t=!1){return(n,s,i,{latestValues:o},r)=>{const l=(Me(n)?gr:lr)(s,o,r,n),c=hr(s,typeof n=="string",t),u=n!==P.Fragment?{...c,...l,ref:i}:{},{children:h}=s,f=P.useMemo(()=>E(h)?h.get():h,[h]);return P.createElement(n,{...u,children:f})}}function Ss(t,{style:e,vars:n},s,i){Object.assign(t.style,e,i&&i.getProjectionStyles(s));for(const o in n)t.style.setProperty(o,n[o])}const Vs=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function ws(t,e,n,s){Ss(t,e,void 0,s);for(const i in e.attrs)t.setAttribute(Vs.has(i)?i:we(i),e.attrs[i])}function Oe(t,e,n){var s;const{style:i}=t,o={};for(const r in i)(E(i[r])||e.style&&E(e.style[r])||gs(r,t)||((s=n?.getValue(r))===null||s===void 0?void 0:s.liveStyle)!==void 0)&&(o[r]=i[r]);return o}function As(t,e,n){const s=Oe(t,e,n);for(const i in t)if(E(t[i])||E(e[i])){const o=wt.indexOf(i)!==-1?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i;s[o]=t[i]}return s}function je(t,e,n,s={},i={}){return typeof e=="function"&&(e=e(n!==void 0?n:t.custom,s,i)),typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"&&(e=e(n!==void 0?n:t.custom,s,i)),e}function vr(t){const e=P.useRef(null);return e.current===null&&(e.current=t()),e.current}const ce=t=>Array.isArray(t),xr=t=>!!(t&&typeof t=="object"&&t.mix&&t.toValue),Tr=t=>ce(t)?t[t.length-1]||0:t;function Lt(t){const e=E(t)?t.get():t;return xr(e)?e.toValue():e}function Pr({scrapeMotionValuesFromProps:t,createRenderState:e,onMount:n},s,i,o){const r={latestValues:br(s,i,o,t),renderState:e()};return n&&(r.mount=a=>n(s,a,r)),r}const Cs=t=>(e,n)=>{const s=P.useContext(Wt),i=P.useContext(Se),o=()=>Pr(t,e,s,i);return n?o():vr(o)};function br(t,e,n,s){const i={},o=s(t,{});for(const f in o)i[f]=Lt(o[f]);let{initial:r,animate:a}=t;const l=Gt(t),c=ds(t);e&&c&&!l&&t.inherit!==!1&&(r===void 0&&(r=e.initial),a===void 0&&(a=e.animate));let u=n?n.initial===!1:!1;u=u||r===!1;const h=u?a:r;return h&&typeof h!="boolean"&&!_t(h)&&(Array.isArray(h)?h:[h]).forEach(d=>{const m=je(t,d);if(!m)return;const{transitionEnd:p,transition:y,...v}=m;for(const T in v){let g=v[T];if(Array.isArray(g)){const b=u?g.length-1:0;g=g[b]}g!==null&&(i[T]=g)}for(const T in p)i[T]=p[T]}),i}const L=t=>t,{schedule:D,cancel:J,state:C,steps:qt}=fs(typeof requestAnimationFrame<"u"?requestAnimationFrame:L,!0),Sr={useVisualState:Cs({scrapeMotionValuesFromProps:As,createRenderState:bs,onMount:(t,e,{renderState:n,latestValues:s})=>{D.read(()=>{try{n.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),D.render(()=>{Be(n,s,{enableHardwareAcceleration:!1},ke(e.tagName),t.transformTemplate),ws(e,n)})}})},Vr={useVisualState:Cs({scrapeMotionValuesFromProps:Oe,createRenderState:Fe})};function wr(t,{forwardMotionProps:e=!1},n,s){return{...Me(t)?Sr:Vr,preloadedFeatures:n,useRender:yr(e),createVisualElement:s,Component:t}}function K(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}const Ds=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1;function $t(t,e="page"){return{point:{x:t[e+"X"],y:t[e+"Y"]}}}const Ar=t=>e=>Ds(e)&&t(e,$t(e));function _(t,e,n,s){return K(t,e,Ar(n),s)}const Cr=(t,e)=>n=>e(t(n)),G=(...t)=>t.reduce(Cr);function Ms(t){let e=null;return()=>{const n=()=>{e=null};return e===null?(e=t,n):!1}}const on=Ms("dragHorizontal"),an=Ms("dragVertical");function Rs(t){let e=!1;if(t==="y")e=an();else if(t==="x")e=on();else{const n=on(),s=an();n&&s?e=()=>{n(),s()}:(n&&n(),s&&s())}return e}function Es(){const t=Rs(!0);return t?(t(),!1):!0}class et{constructor(e){this.isMounted=!1,this.node=e}update(){}}function ln(t,e){const n="pointer"+(e?"enter":"leave"),s="onHover"+(e?"Start":"End"),i=(o,r)=>{if(o.pointerType==="touch"||Es())return;const a=t.getProps();t.animationState&&a.whileHover&&t.animationState.setActive("whileHover",e),a[s]&&a[s](o,r)};return _(t.current,n,i,{passive:!t.getProps()[s]})}class Dr extends et{mount(){this.unmount=G(ln(this.node,!0),ln(this.node,!1))}unmount(){}}class Mr extends et{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=G(K(this.node.current,"focus",()=>this.onFocus()),K(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const Ls=(t,e)=>e?t===e?!0:Ls(t,e.parentElement):!1;function Zt(t,e){if(!e)return;const n=new PointerEvent("pointer"+t);e(n,$t(n))}class Rr extends et{constructor(){super(...arguments),this.removeStartListeners=L,this.removeEndListeners=L,this.removeAccessibleListeners=L,this.startPointerPress=(e,n)=>{if(this.isPressing)return;this.removeEndListeners();const s=this.node.getProps(),o=_(window,"pointerup",(a,l)=>{if(!this.checkPressEnd())return;const{onTap:c,onTapCancel:u,globalTapTarget:h}=this.node.getProps();!h&&!Ls(this.node.current,a.target)?u&&u(a,l):c&&c(a,l)},{passive:!(s.onTap||s.onPointerUp)}),r=_(window,"pointercancel",(a,l)=>this.cancelPress(a,l),{passive:!(s.onTapCancel||s.onPointerCancel)});this.removeEndListeners=G(o,r),this.startPress(e,n)},this.startAccessiblePress=()=>{const e=o=>{if(o.key!=="Enter"||this.isPressing)return;const r=a=>{a.key!=="Enter"||!this.checkPressEnd()||Zt("up",(l,c)=>{const{onTap:u}=this.node.getProps();u&&u(l,c)})};this.removeEndListeners(),this.removeEndListeners=K(this.node.current,"keyup",r),Zt("down",(a,l)=>{this.startPress(a,l)})},n=K(this.node.current,"keydown",e),s=()=>{this.isPressing&&Zt("cancel",(o,r)=>this.cancelPress(o,r))},i=K(this.node.current,"blur",s);this.removeAccessibleListeners=G(n,i)}}startPress(e,n){this.isPressing=!0;const{onTapStart:s,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),s&&s(e,n)}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!Es()}cancelPress(e,n){if(!this.checkPressEnd())return;const{onTapCancel:s}=this.node.getProps();s&&s(e,n)}mount(){const e=this.node.getProps(),n=_(e.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),s=K(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=G(n,s)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const ue=new WeakMap,Qt=new WeakMap,Er=t=>{const e=ue.get(t.target);e&&e(t)},Lr=t=>{t.forEach(Er)};function Fr({root:t,...e}){const n=t||document;Qt.has(n)||Qt.set(n,{});const s=Qt.get(n),i=JSON.stringify(e);return s[i]||(s[i]=new IntersectionObserver(Lr,{root:t,...e})),s[i]}function Br(t,e,n){const s=Fr(e);return ue.set(t,n),s.observe(t),()=>{ue.delete(t),s.unobserve(t)}}const kr={some:0,all:1};class Or extends et{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:n,margin:s,amount:i="some",once:o}=e,r={root:n?n.current:void 0,rootMargin:s,threshold:typeof i=="number"?i:kr[i]},a=l=>{const{isIntersecting:c}=l;if(this.isInView===c||(this.isInView=c,o&&!c&&this.hasEnteredView))return;c&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",c);const{onViewportEnter:u,onViewportLeave:h}=this.node.getProps(),f=c?u:h;f&&f(l)};return Br(this.node.current,r,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:n}=this.node;["amount","margin","root"].some(jr(e,n))&&this.startObserver()}unmount(){}}function jr({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}const Ir={inView:{Feature:Or},tap:{Feature:Rr},focus:{Feature:Mr},hover:{Feature:Dr}};function Fs(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}function Nr(t){const e={};return t.values.forEach((n,s)=>e[s]=n.get()),e}function Ur(t){const e={};return t.values.forEach((n,s)=>e[s]=n.getVelocity()),e}function zt(t,e,n){const s=t.getProps();return je(s,e,n!==void 0?n:s.custom,Nr(t),Ur(t))}const q=t=>t*1e3,$=t=>t/1e3,Kr={type:"spring",stiffness:500,damping:25,restSpeed:10},Wr=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),_r={type:"keyframes",duration:.8},Gr={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},$r=(t,{keyframes:e})=>e.length>2?_r:at.has(t)?t.startsWith("scale")?Wr(e[1]):Kr:Gr;function zr({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:c,...u}){return!!Object.keys(u).length}function Ie(t,e){return t[e]||t.default||t}const Hr=t=>t!==null;function Ht(t,{repeat:e,repeatType:n="loop"},s){const i=t.filter(Hr),o=e&&n!=="loop"&&e%2===1?0:i.length-1;return!o||s===void 0?i[o]:s}let Ft;function Yr(){Ft=void 0}const Z={now:()=>(Ft===void 0&&Z.set(C.isProcessing||Oi.useManualTiming?C.timestamp:performance.now()),Ft),set:t=>{Ft=t,queueMicrotask(Yr)}},Bs=t=>/^0[^.\s]+$/u.test(t);function Xr(t){return typeof t=="number"?t===0:t!==null?t==="none"||t==="0"||Bs(t):!0}let ks=L;const Os=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),qr=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Zr(t){const e=qr.exec(t);if(!e)return[,];const[,n,s,i]=e;return[`--${n??s}`,i]}function js(t,e,n=1){const[s,i]=Zr(t);if(!s)return;const o=window.getComputedStyle(e).getPropertyValue(s);if(o){const r=o.trim();return Os(r)?parseFloat(r):r}return Re(i)?js(i,e,n+1):i}const Qr=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),cn=t=>t===mt||t===x,un=(t,e)=>parseFloat(t.split(", ")[e]),hn=(t,e)=>(n,{transform:s})=>{if(s==="none"||!s)return 0;const i=s.match(/^matrix3d\((.+)\)$/u);if(i)return un(i[1],e);{const o=s.match(/^matrix\((.+)\)$/u);return o?un(o[1],t):0}},Jr=new Set(["x","y","z"]),to=wt.filter(t=>!Jr.has(t));function fn(t){const e=[];return to.forEach(n=>{const s=t.getValue(n);s!==void 0&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e}const dt={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:hn(4,13),y:hn(5,14)};dt.translateX=dt.x;dt.translateY=dt.y;const Is=t=>e=>e.test(t),eo={test:t=>t==="auto",parse:t=>t},Ns=[mt,x,N,H,rr,ir,eo],dn=t=>Ns.find(Is(t)),ot=new Set;let he=!1,fe=!1;function Us(){if(fe){const t=Array.from(ot).filter(s=>s.needsMeasurement),e=new Set(t.map(s=>s.element)),n=new Map;e.forEach(s=>{fn(s).length&&(n.set(s,fn(s)),s.render())}),t.forEach(s=>s.measureInitialState()),e.forEach(s=>{s.render()}),t.forEach(s=>s.measureEndState()),t.forEach(s=>{s.suspendedScrollY!==void 0&&window.scrollTo(0,s.suspendedScrollY)})}fe=!1,he=!1,ot.forEach(t=>t.complete()),ot.clear()}function Ks(){ot.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(fe=!0)})}function no(){Ks(),Us()}class Ne{constructor(e,n,s,i,o,r=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...e],this.onComplete=n,this.name=s,this.motionValue=i,this.element=o,this.isAsync=r}scheduleResolve(){this.isScheduled=!0,this.isAsync?(ot.add(this),he||(he=!0,D.read(Ks),D.resolveKeyframes(Us))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:n,element:s,motionValue:i}=this;for(let o=0;o<e.length;o++)if(e[o]===null)if(o===0){const r=i?.get(),a=e[e.length-1];if(r!==void 0)e[0]=r;else if(s&&n){const l=s.readValue(n,a);l!=null&&(e[0]=l)}e[0]===void 0&&(e[0]=a),i&&r===void 0&&i.set(e[0])}else e[o]=e[o-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),ot.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,ot.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const Ue=(t,e)=>n=>!!(At(n)&&sr.test(n)&&n.startsWith(t)||e&&Object.prototype.hasOwnProperty.call(n,e)),Ws=(t,e,n)=>s=>{if(!At(s))return s;const[i,o,r,a]=s.match(Ee);return{[t]:parseFloat(i),[e]:parseFloat(o),[n]:parseFloat(r),alpha:a!==void 0?parseFloat(a):1}},so=t=>Q(0,255,t),Jt={...mt,transform:t=>Math.round(so(t))},rt={test:Ue("rgb","red"),parse:Ws("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+Jt.transform(t)+", "+Jt.transform(e)+", "+Jt.transform(n)+", "+vt(yt.transform(s))+")"};function io(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}}const de={test:Ue("#"),parse:io,transform:rt.transform},ut={test:Ue("hsl","hue"),parse:Ws("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+N.transform(vt(e))+", "+N.transform(vt(n))+", "+vt(yt.transform(s))+")"},R={test:t=>rt.test(t)||de.test(t)||ut.test(t),parse:t=>rt.test(t)?rt.parse(t):ut.test(t)?ut.parse(t):de.parse(t),transform:t=>At(t)?t:t.hasOwnProperty("red")?rt.transform(t):ut.transform(t)};function ro(t){var e,n;return isNaN(t)&&At(t)&&(((e=t.match(Ee))===null||e===void 0?void 0:e.length)||0)+(((n=t.match(nr))===null||n===void 0?void 0:n.length)||0)>0}const _s="number",Gs="color",oo="var",ao="var(",mn="${}",lo=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function jt(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},i=[];let o=0;const a=e.replace(lo,l=>(R.test(l)?(s.color.push(o),i.push(Gs),n.push(R.parse(l))):l.startsWith(ao)?(s.var.push(o),i.push(oo),n.push(l)):(s.number.push(o),i.push(_s),n.push(parseFloat(l))),++o,mn)).split(mn);return{values:n,split:a,indexes:s,types:i}}function $s(t){return jt(t).values}function zs(t){const{split:e,types:n}=jt(t),s=e.length;return i=>{let o="";for(let r=0;r<s;r++)if(o+=e[r],i[r]!==void 0){const a=n[r];a===_s?o+=vt(i[r]):a===Gs?o+=R.transform(i[r]):o+=i[r]}return o}}const co=t=>typeof t=="number"?0:t;function uo(t){const e=$s(t);return zs(t)(e.map(co))}const tt={test:ro,parse:$s,createTransformer:zs,getAnimatableNone:uo},ho=new Set(["brightness","contrast","saturate","opacity"]);function fo(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[s]=n.match(Ee)||[];if(!s)return t;const i=n.replace(s,"");let o=ho.has(e)?1:0;return s!==n&&(o*=100),e+"("+o+i+")"}const mo=/\b([a-z-]*)\(.*?\)/gu,me={...tt,getAnimatableNone:t=>{const e=t.match(mo);return e?e.map(fo).join(" "):t}},po={...xs,color:R,backgroundColor:R,outlineColor:R,fill:R,stroke:R,borderColor:R,borderTopColor:R,borderRightColor:R,borderBottomColor:R,borderLeftColor:R,filter:me,WebkitFilter:me},Ke=t=>po[t];function Hs(t,e){let n=Ke(t);return n!==me&&(n=tt),n.getAnimatableNone?n.getAnimatableNone(e):void 0}function go(t,e,n){let s=0,i;for(;s<t.length&&!i;)typeof t[s]=="string"&&t[s]!=="none"&&t[s]!=="0"&&(i=t[s]),s++;if(i&&n)for(const o of e)t[o]=Hs(n,i)}class Ys extends Ne{constructor(e,n,s,i){super(e,n,s,i,i?.owner,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:n,name:s}=this;if(!n.current)return;super.readKeyframes();for(let l=0;l<e.length;l++){const c=e[l];if(typeof c=="string"&&Re(c)){const u=js(c,n.current);u!==void 0&&(e[l]=u),l===e.length-1&&(this.finalKeyframe=c)}}if(!Qr.has(s)||e.length!==2)return this.resolveNoneKeyframes();const[i,o]=e,r=dn(i),a=dn(o);if(r!==a)if(cn(r)&&cn(a))for(let l=0;l<e.length;l++){const c=e[l];typeof c=="string"&&(e[l]=parseFloat(c))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:n}=this,s=[];for(let i=0;i<e.length;i++)Xr(e[i])&&s.push(i);s.length&&go(e,s,n)}measureInitialState(){const{element:e,unresolvedKeyframes:n,name:s}=this;if(!e.current)return;s==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=dt[s](e.measureViewportBox(),window.getComputedStyle(e.current)),n[0]=this.measuredOrigin;const i=n[n.length-1];i!==void 0&&e.getValue(s,i).jump(i,!1)}measureEndState(){var e;const{element:n,name:s,unresolvedKeyframes:i}=this;if(!n.current)return;const o=n.getValue(s);o&&o.jump(this.measuredOrigin,!1);const r=i.length-1,a=i[r];i[r]=dt[s](n.measureViewportBox(),window.getComputedStyle(n.current)),a!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=a),!((e=this.removedTransforms)===null||e===void 0)&&e.length&&this.removedTransforms.forEach(([l,c])=>{n.getValue(l).set(c)}),this.resolveNoneKeyframes()}}function yo(t){let e;return()=>(e===void 0&&(e=t()),e)}const pn=(t,e)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(tt.test(t)||t==="0")&&!t.startsWith("url("));function vo(t){const e=t[0];if(t.length===1)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}function xo(t,e,n,s){const i=t[0];if(i===null)return!1;const o=t[t.length-1],r=pn(i,e),a=pn(o,e);return!r||!a?!1:vo(t)||n==="spring"&&s}class Xs{constructor({autoplay:e=!0,delay:n=0,type:s="keyframes",repeat:i=0,repeatDelay:o=0,repeatType:r="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.options={autoplay:e,delay:n,type:s,repeat:i,repeatDelay:o,repeatType:r,...a},this.updateFinishedPromise()}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&no(),this._resolved}onKeyframesResolved(e,n){this.hasAttemptedResolve=!0;const{name:s,type:i,velocity:o,delay:r,onComplete:a,onUpdate:l,isGenerator:c}=this.options;if(!c&&!xo(e,s,i,o))if(r)this.options.duration=0;else{l?.(Ht(e,this.options,n)),a?.(),this.resolveFinishedPromise();return}const u=this.initPlayback(e,n);u!==!1&&(this._resolved={keyframes:e,finalKeyframe:n,...u},this.onPostResolved())}onPostResolved(){}then(e,n){return this.currentFinishedPromise.then(e,n)}updateFinishedPromise(){this.currentFinishedPromise=new Promise(e=>{this.resolveFinishedPromise=e})}}function qs(t,e){return e?t*(1e3/e):0}const To=5;function Zs(t,e,n){const s=Math.max(e-To,0);return qs(n-t(s),e-s)}const te=.001,Po=.01,bo=10,So=.05,Vo=1;function wo({duration:t=800,bounce:e=.25,velocity:n=0,mass:s=1}){let i,o,r=1-e;r=Q(So,Vo,r),t=Q(Po,bo,$(t)),r<1?(i=c=>{const u=c*r,h=u*t,f=u-n,d=pe(c,r),m=Math.exp(-h);return te-f/d*m},o=c=>{const h=c*r*t,f=h*n+n,d=Math.pow(r,2)*Math.pow(c,2)*t,m=Math.exp(-h),p=pe(Math.pow(c,2),r);return(-i(c)+te>0?-1:1)*((f-d)*m)/p}):(i=c=>{const u=Math.exp(-c*t),h=(c-n)*t+1;return-te+u*h},o=c=>{const u=Math.exp(-c*t),h=(n-c)*(t*t);return u*h});const a=5/t,l=Co(i,o,a);if(t=q(t),isNaN(l))return{stiffness:100,damping:10,duration:t};{const c=Math.pow(l,2)*s;return{stiffness:c,damping:r*2*Math.sqrt(s*c),duration:t}}}const Ao=12;function Co(t,e,n){let s=n;for(let i=1;i<Ao;i++)s=s-t(s)/e(s);return s}function pe(t,e){return t*Math.sqrt(1-e*e)}const Do=["duration","bounce"],Mo=["stiffness","damping","mass"];function gn(t,e){return e.some(n=>t[n]!==void 0)}function Ro(t){let e={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...t};if(!gn(t,Mo)&&gn(t,Do)){const n=wo(t);e={...e,...n,mass:1},e.isResolvedFromDuration=!0}return e}function Qs({keyframes:t,restDelta:e,restSpeed:n,...s}){const i=t[0],o=t[t.length-1],r={done:!1,value:i},{stiffness:a,damping:l,mass:c,duration:u,velocity:h,isResolvedFromDuration:f}=Ro({...s,velocity:-$(s.velocity||0)}),d=h||0,m=l/(2*Math.sqrt(a*c)),p=o-i,y=$(Math.sqrt(a/c)),v=Math.abs(p)<5;n||(n=v?.01:2),e||(e=v?.005:.5);let T;if(m<1){const g=pe(y,m);T=b=>{const V=Math.exp(-m*y*b);return o-V*((d+m*y*p)/g*Math.sin(g*b)+p*Math.cos(g*b))}}else if(m===1)T=g=>o-Math.exp(-y*g)*(p+(d+y*p)*g);else{const g=y*Math.sqrt(m*m-1);T=b=>{const V=Math.exp(-m*y*b),M=Math.min(g*b,300);return o-V*((d+m*y*p)*Math.sinh(M)+g*p*Math.cosh(M))/g}}return{calculatedDuration:f&&u||null,next:g=>{const b=T(g);if(f)r.done=g>=u;else{let V=d;g!==0&&(m<1?V=Zs(T,g,b):V=0);const M=Math.abs(V)<=n,z=Math.abs(o-b)<=e;r.done=M&&z}return r.value=r.done?o:b,r}}}function yn({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:c=.5,restSpeed:u}){const h=t[0],f={done:!1,value:h},d=S=>a!==void 0&&S<a||l!==void 0&&S>l,m=S=>a===void 0?l:l===void 0||Math.abs(a-S)<Math.abs(l-S)?a:l;let p=n*e;const y=h+p,v=r===void 0?y:r(y);v!==y&&(p=v-h);const T=S=>-p*Math.exp(-S/s),g=S=>v+T(S),b=S=>{const k=T(S),O=g(S);f.done=Math.abs(k)<=c,f.value=f.done?v:O};let V,M;const z=S=>{d(f.value)&&(V=S,M=Qs({keyframes:[f.value,m(f.value)],velocity:Zs(g,S,f.value),damping:i,stiffness:o,restDelta:c,restSpeed:u}))};return z(0),{calculatedDuration:null,next:S=>{let k=!1;return!M&&V===void 0&&(k=!0,b(S),z(S)),V!==void 0&&S>=V?M.next(S-V):(!k&&b(S),f)}}}const Js=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,Eo=1e-7,Lo=12;function Fo(t,e,n,s,i){let o,r,a=0;do r=e+(n-e)/2,o=Js(r,s,i)-t,o>0?n=r:e=r;while(Math.abs(o)>Eo&&++a<Lo);return r}function Dt(t,e,n,s){if(t===e&&n===s)return L;const i=o=>Fo(o,0,1,t,n);return o=>o===0||o===1?o:Js(i(o),e,s)}const Bo=Dt(.42,0,1,1),ko=Dt(0,0,.58,1),ti=Dt(.42,0,.58,1),Oo=t=>Array.isArray(t)&&typeof t[0]!="number",ei=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,ni=t=>e=>1-t(1-e),We=t=>1-Math.sin(Math.acos(t)),si=ni(We),jo=ei(We),ii=Dt(.33,1.53,.69,.99),_e=ni(ii),Io=ei(_e),No=t=>(t*=2)<1?.5*_e(t):.5*(2-Math.pow(2,-10*(t-1))),Uo={linear:L,easeIn:Bo,easeInOut:ti,easeOut:ko,circIn:We,circInOut:jo,circOut:si,backIn:_e,backInOut:Io,backOut:ii,anticipate:No},vn=t=>{if(Array.isArray(t)){ks(t.length===4);const[e,n,s,i]=t;return Dt(e,n,s,i)}else if(typeof t=="string")return Uo[t];return t},St=(t,e,n)=>{const s=e-t;return s===0?1:(n-t)/s},w=(t,e,n)=>t+(e-t)*n;function ee(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Ko({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,e/=100,n/=100;let i=0,o=0,r=0;if(!e)i=o=r=n;else{const a=n<.5?n*(1+e):n+e-n*e,l=2*n-a;i=ee(l,a,t+1/3),o=ee(l,a,t),r=ee(l,a,t-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(r*255),alpha:s}}const ne=(t,e,n)=>{const s=t*t,i=n*(e*e-s)+s;return i<0?0:Math.sqrt(i)},Wo=[de,rt,ut],_o=t=>Wo.find(e=>e.test(t));function xn(t){const e=_o(t);let n=e.parse(t);return e===ut&&(n=Ko(n)),n}const Tn=(t,e)=>{const n=xn(t),s=xn(e),i={...n};return o=>(i.red=ne(n.red,s.red,o),i.green=ne(n.green,s.green,o),i.blue=ne(n.blue,s.blue,o),i.alpha=w(n.alpha,s.alpha,o),rt.transform(i))};function ge(t,e){return n=>n>0?e:t}function Go(t,e){return n=>w(t,e,n)}function Ge(t){return typeof t=="number"?Go:typeof t=="string"?Re(t)?ge:R.test(t)?Tn:Ho:Array.isArray(t)?ri:typeof t=="object"?R.test(t)?Tn:$o:ge}function ri(t,e){const n=[...t],s=n.length,i=t.map((o,r)=>Ge(o)(o,e[r]));return o=>{for(let r=0;r<s;r++)n[r]=i[r](o);return n}}function $o(t,e){const n={...t,...e},s={};for(const i in n)t[i]!==void 0&&e[i]!==void 0&&(s[i]=Ge(t[i])(t[i],e[i]));return i=>{for(const o in s)n[o]=s[o](i);return n}}function zo(t,e){var n;const s=[],i={color:0,var:0,number:0};for(let o=0;o<e.values.length;o++){const r=e.types[o],a=t.indexes[r][i[r]],l=(n=t.values[a])!==null&&n!==void 0?n:0;s[o]=l,i[r]++}return s}const Ho=(t,e)=>{const n=tt.createTransformer(e),s=jt(t),i=jt(e);return s.indexes.var.length===i.indexes.var.length&&s.indexes.color.length===i.indexes.color.length&&s.indexes.number.length>=i.indexes.number.length?G(ri(zo(s,i),i.values),n):ge(t,e)};function oi(t,e,n){return typeof t=="number"&&typeof e=="number"&&typeof n=="number"?w(t,e,n):Ge(t)(t,e)}function Yo(t,e,n){const s=[],i=n||oi,o=t.length-1;for(let r=0;r<o;r++){let a=i(t[r],t[r+1]);if(e){const l=Array.isArray(e)?e[r]||L:e;a=G(l,a)}s.push(a)}return s}function Xo(t,e,{clamp:n=!0,ease:s,mixer:i}={}){const o=t.length;if(ks(o===e.length),o===1)return()=>e[0];if(o===2&&t[0]===t[1])return()=>e[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const r=Yo(e,s,i),a=r.length,l=c=>{let u=0;if(a>1)for(;u<t.length-2&&!(c<t[u+1]);u++);const h=St(t[u],t[u+1],c);return r[u](h)};return n?c=>l(Q(t[0],t[o-1],c)):l}function qo(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=St(0,e,s);t.push(w(n,1,i))}}function Zo(t){const e=[0];return qo(e,t.length-1),e}function Qo(t,e){return t.map(n=>n*e)}function Jo(t,e){return t.map(()=>e||ti).splice(0,t.length-1)}function It({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=Oo(s)?s.map(vn):vn(s),o={done:!1,value:e[0]},r=Qo(n&&n.length===e.length?n:Zo(e),t),a=Xo(r,e,{ease:Array.isArray(i)?i:Jo(e,i)});return{calculatedDuration:t,next:l=>(o.value=a(l),o.done=l>=t,o)}}const Pn=2e4;function ta(t){let e=0;const n=50;let s=t.next(e);for(;!s.done&&e<Pn;)e+=n,s=t.next(e);return e>=Pn?1/0:e}const ea=t=>{const e=({timestamp:n})=>t(n);return{start:()=>D.update(e,!0),stop:()=>J(e),now:()=>C.isProcessing?C.timestamp:Z.now()}},na={decay:yn,inertia:yn,tween:It,keyframes:It,spring:Qs},sa=t=>t/100;class $e extends Xs{constructor({KeyframeResolver:e=Ne,...n}){super(n),this.holdTime=null,this.startTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.state="idle";const{name:s,motionValue:i,keyframes:o}=this.options,r=(a,l)=>this.onKeyframesResolved(a,l);s&&i&&i.owner?this.resolver=i.owner.resolveKeyframes(o,r,s,i):this.resolver=new e(o,r,s,i),this.resolver.scheduleResolve()}initPlayback(e){const{type:n="keyframes",repeat:s=0,repeatDelay:i=0,repeatType:o,velocity:r=0}=this.options,a=na[n]||It;let l,c;a!==It&&typeof e[0]!="number"&&(l=G(sa,oi(e[0],e[1])),e=[0,100]);const u=a({...this.options,keyframes:e});o==="mirror"&&(c=a({...this.options,keyframes:[...e].reverse(),velocity:-r})),u.calculatedDuration===null&&(u.calculatedDuration=ta(u));const{calculatedDuration:h}=u,f=h+i,d=f*(s+1)-i;return{generator:u,mirroredGenerator:c,mapPercentToKeyframes:l,calculatedDuration:h,resolvedDuration:f,totalDuration:d}}onPostResolved(){const{autoplay:e=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!e?this.pause():this.state=this.pendingPlayState}tick(e,n=!1){const{resolved:s}=this;if(!s){const{keyframes:S}=this.options;return{done:!0,value:S[S.length-1]}}const{finalKeyframe:i,generator:o,mirroredGenerator:r,mapPercentToKeyframes:a,keyframes:l,calculatedDuration:c,totalDuration:u,resolvedDuration:h}=s;if(this.startTime===null)return o.next(0);const{delay:f,repeat:d,repeatType:m,repeatDelay:p,onUpdate:y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-u/this.speed,this.startTime)),n?this.currentTime=e:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(e-this.startTime)*this.speed;const v=this.currentTime-f*(this.speed>=0?1:-1),T=this.speed>=0?v<0:v>u;this.currentTime=Math.max(v,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=u);let g=this.currentTime,b=o;if(d){const S=Math.min(this.currentTime,u)/h;let k=Math.floor(S),O=S%1;!O&&S>=1&&(O=1),O===1&&k--,k=Math.min(k,d+1),!!(k%2)&&(m==="reverse"?(O=1-O,p&&(O-=p/h)):m==="mirror"&&(b=r)),g=Q(0,1,O)*h}const V=T?{done:!1,value:l[0]}:b.next(g);a&&(V.value=a(V.value));let{done:M}=V;!T&&c!==null&&(M=this.speed>=0?this.currentTime>=u:this.currentTime<=0);const z=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&M);return z&&i!==void 0&&(V.value=Ht(l,this.options,i)),y&&y(V.value),z&&this.finish(),V}get duration(){const{resolved:e}=this;return e?$(e.calculatedDuration):0}get time(){return $(this.currentTime)}set time(e){e=q(e),this.currentTime=e,this.holdTime!==null||this.speed===0?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.speed)}get speed(){return this.playbackSpeed}set speed(e){const n=this.playbackSpeed!==e;this.playbackSpeed=e,n&&(this.time=$(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:e=ea,onPlay:n}=this.options;this.driver||(this.driver=e(i=>this.tick(i))),n&&n();const s=this.driver.now();this.holdTime!==null?this.startTime=s-this.holdTime:(!this.startTime||this.state==="finished")&&(this.startTime=s),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var e;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(e=this.currentTime)!==null&&e!==void 0?e:0}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:e}=this.options;e&&e()}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:e}=this.options;e&&e()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}}const ai=t=>Array.isArray(t)&&typeof t[0]=="number";function li(t){return!!(!t||typeof t=="string"&&ci[t]||ai(t)||Array.isArray(t)&&t.every(li))}const gt=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,ci={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:gt([0,.65,.55,1]),circOut:gt([.55,0,1,.45]),backIn:gt([.31,.01,.66,-.59]),backOut:gt([.33,1.53,.69,.99])};function ui(t){if(t)return ai(t)?gt(t):Array.isArray(t)?t.map(ui):ci[t]}function ia(t,e,n,{delay:s=0,duration:i=300,repeat:o=0,repeatType:r="loop",ease:a,times:l}={}){const c={[e]:n};l&&(c.offset=l);const u=ui(a);return Array.isArray(u)&&(c.easing=u),t.animate(c,{delay:s,duration:i,easing:Array.isArray(u)?"linear":u,fill:"both",iterations:o+1,direction:r==="reverse"?"alternate":"normal"})}const ra=yo(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),oa=new Set(["opacity","clipPath","filter","transform"]),Nt=10,aa=2e4;function la(t){return t.type==="spring"||t.name==="backgroundColor"||!li(t.ease)}function ca(t,e){const n=new $e({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0});let s={done:!1,value:t[0]};const i=[];let o=0;for(;!s.done&&o<aa;)s=n.sample(o),i.push(s.value),o+=Nt;return{times:void 0,keyframes:i,duration:o-Nt,ease:"linear"}}class bn extends Xs{constructor(e){super(e);const{name:n,motionValue:s,keyframes:i}=this.options;this.resolver=new Ys(i,(o,r)=>this.onKeyframesResolved(o,r),n,s),this.resolver.scheduleResolve()}initPlayback(e,n){var s;let{duration:i=300,times:o,ease:r,type:a,motionValue:l,name:c}=this.options;if(!(!((s=l.owner)===null||s===void 0)&&s.current))return!1;if(la(this.options)){const{onComplete:h,onUpdate:f,motionValue:d,...m}=this.options,p=ca(e,m);e=p.keyframes,e.length===1&&(e[1]=e[0]),i=p.duration,o=p.times,r=p.ease,a="keyframes"}const u=ia(l.owner.current,c,e,{...this.options,duration:i,times:o,ease:r});return u.startTime=Z.now(),this.pendingTimeline?(u.timeline=this.pendingTimeline,this.pendingTimeline=void 0):u.onfinish=()=>{const{onComplete:h}=this.options;l.set(Ht(e,this.options,n)),h&&h(),this.cancel(),this.resolveFinishedPromise()},{animation:u,duration:i,times:o,type:a,ease:r,keyframes:e}}get duration(){const{resolved:e}=this;if(!e)return 0;const{duration:n}=e;return $(n)}get time(){const{resolved:e}=this;if(!e)return 0;const{animation:n}=e;return $(n.currentTime||0)}set time(e){const{resolved:n}=this;if(!n)return;const{animation:s}=n;s.currentTime=q(e)}get speed(){const{resolved:e}=this;if(!e)return 1;const{animation:n}=e;return n.playbackRate}set speed(e){const{resolved:n}=this;if(!n)return;const{animation:s}=n;s.playbackRate=e}get state(){const{resolved:e}=this;if(!e)return"idle";const{animation:n}=e;return n.playState}attachTimeline(e){if(!this._resolved)this.pendingTimeline=e;else{const{resolved:n}=this;if(!n)return L;const{animation:s}=n;s.timeline=e,s.onfinish=null}return L}play(){if(this.isStopped)return;const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.playState==="finished"&&this.updateFinishedPromise(),n.play()}pause(){const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;const{resolved:e}=this;if(!e)return;const{animation:n,keyframes:s,duration:i,type:o,ease:r,times:a}=e;if(!(n.playState==="idle"||n.playState==="finished")){if(this.time){const{motionValue:l,onUpdate:c,onComplete:u,...h}=this.options,f=new $e({...h,keyframes:s,duration:i,type:o,ease:r,times:a,isGenerator:!0}),d=q(this.time);l.setWithVelocity(f.sample(d-Nt).value,f.sample(d).value,Nt)}this.cancel()}}complete(){const{resolved:e}=this;e&&e.animation.finish()}cancel(){const{resolved:e}=this;e&&e.animation.cancel()}static supports(e){const{motionValue:n,name:s,repeatDelay:i,repeatType:o,damping:r,type:a}=e;return ra()&&s&&oa.has(s)&&n&&n.owner&&n.owner.current instanceof HTMLElement&&!n.owner.getProps().onUpdate&&!i&&o!=="mirror"&&r!==0&&a!=="inertia"}}const ze=(t,e,n,s={},i,o)=>r=>{const a=Ie(s,t)||{},l=a.delay||s.delay||0;let{elapsed:c=0}=s;c=c-q(l);let u={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:f=>{e.set(f),a.onUpdate&&a.onUpdate(f)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:i};zr(a)||(u={...u,...$r(t,u)}),u.duration&&(u.duration=q(u.duration)),u.repeatDelay&&(u.repeatDelay=q(u.repeatDelay)),u.from!==void 0&&(u.keyframes[0]=u.from);let h=!1;if((u.type===!1||u.duration===0&&!u.repeatDelay)&&(u.duration=0,u.delay===0&&(h=!0)),h&&!o&&e.get()!==void 0){const f=Ht(u.keyframes,a);if(f!==void 0){D.update(()=>{u.onUpdate(f),u.onComplete()});return}}return!o&&bn.supports(u)?new bn(u):new $e(u)};function Ut(t){return!!(E(t)&&t.add)}function He(t,e){t.indexOf(e)===-1&&t.push(e)}function Ye(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}class Xe{constructor(){this.subscriptions=[]}add(e){return He(this.subscriptions,e),()=>Ye(this.subscriptions,e)}notify(e,n,s){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](e,n,s);else for(let o=0;o<i;o++){const r=this.subscriptions[o];r&&r(e,n,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Sn=30,ua=t=>!isNaN(parseFloat(t));class ha{constructor(e,n={}){this.version="11.0.28",this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(s,i=!0)=>{const o=Z.now();this.updatedAt!==o&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(s),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.canTrackVelocity=ua(this.current),this.owner=n.owner}setCurrent(e){this.current=e,this.updatedAt=Z.now()}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,n){this.events[e]||(this.events[e]=new Xe);const s=this.events[e].add(n);return e==="change"?()=>{s(),D.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,n){this.passiveEffect=e,this.stopPassiveEffect=n}set(e,n=!0){!n||!this.passiveEffect?this.updateAndNotify(e,n):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,n,s){this.set(n),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-s}jump(e,n=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const e=Z.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||e-this.updatedAt>Sn)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,Sn);return qs(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Vt(t,e){return new ha(t,e)}function fa(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,Vt(n))}function da(t,e){const n=zt(t,e);let{transitionEnd:s={},transition:i={},...o}=n||{};o={...o,...s};for(const r in o){const a=Tr(o[r]);fa(t,r,a)}}function ma({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,s}function hi(t,e,{delay:n=0,transitionOverride:s,type:i}={}){var o;let{transition:r=t.getDefaultTransition(),transitionEnd:a,...l}=e;const c=t.getValue("willChange");s&&(r=s);const u=[],h=i&&t.animationState&&t.animationState.getState()[i];for(const f in l){const d=t.getValue(f,(o=t.latestValues[f])!==null&&o!==void 0?o:null),m=l[f];if(m===void 0||h&&ma(h,f))continue;const p={delay:n,elapsed:0,...Ie(r||{},f)};let y=!1;if(window.HandoffAppearAnimations){const T=t.getProps()[hs];if(T){const g=window.HandoffAppearAnimations(T,f);g!==null&&(p.elapsed=g,y=!0)}}d.start(ze(f,d,m,t.shouldReduceMotion&&at.has(f)?{type:!1}:p,t,y));const v=d.animation;v&&(Ut(c)&&(c.add(f),v.then(()=>c.remove(f))),u.push(v))}return a&&Promise.all(u).then(()=>{D.update(()=>{a&&da(t,a)})}),u}function ye(t,e,n={}){var s;const i=zt(t,e,n.type==="exit"?(s=t.presenceContext)===null||s===void 0?void 0:s.custom:void 0);let{transition:o=t.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(o=n.transitionOverride);const r=i?()=>Promise.all(hi(t,i,n)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(c=0)=>{const{delayChildren:u=0,staggerChildren:h,staggerDirection:f}=o;return pa(t,e,u+c,h,f,n)}:()=>Promise.resolve(),{when:l}=o;if(l){const[c,u]=l==="beforeChildren"?[r,a]:[a,r];return c().then(()=>u())}else return Promise.all([r(),a(n.delay)])}function pa(t,e,n=0,s=0,i=1,o){const r=[],a=(t.variantChildren.size-1)*s,l=i===1?(c=0)=>c*s:(c=0)=>a-c*s;return Array.from(t.variantChildren).sort(ga).forEach((c,u)=>{c.notify("AnimationStart",e),r.push(ye(c,e,{...o,delay:n+l(u)}).then(()=>c.notify("AnimationComplete",e)))}),Promise.all(r)}function ga(t,e){return t.sortNodePosition(e)}function ya(t,e,n={}){t.notify("AnimationStart",e);let s;if(Array.isArray(e)){const i=e.map(o=>ye(t,o,n));s=Promise.all(i)}else if(typeof e=="string")s=ye(t,e,n);else{const i=typeof e=="function"?zt(t,e,n.custom):e;s=Promise.all(hi(t,i,n))}return s.then(()=>{D.postRender(()=>{t.notify("AnimationComplete",e)})})}const va=[...Ce].reverse(),xa=Ce.length;function Ta(t){return e=>Promise.all(e.map(({animation:n,options:s})=>ya(t,n,s)))}function Pa(t){let e=Ta(t);const n=Sa();let s=!0;const i=l=>(c,u)=>{var h;const f=zt(t,u,l==="exit"?(h=t.presenceContext)===null||h===void 0?void 0:h.custom:void 0);if(f){const{transition:d,transitionEnd:m,...p}=f;c={...c,...p,...m}}return c};function o(l){e=l(t)}function r(l){const c=t.getProps(),u=t.getVariantContext(!0)||{},h=[],f=new Set;let d={},m=1/0;for(let y=0;y<xa;y++){const v=va[y],T=n[v],g=c[v]!==void 0?c[v]:u[v],b=Pt(g),V=v===l?T.isActive:null;V===!1&&(m=y);let M=g===u[v]&&g!==c[v]&&b;if(M&&s&&t.manuallyAnimateOnMount&&(M=!1),T.protectedKeys={...d},!T.isActive&&V===null||!g&&!T.prevProp||_t(g)||typeof g=="boolean")continue;let S=ba(T.prevProp,g)||v===l&&T.isActive&&!M&&b||y>m&&b,k=!1;const O=Array.isArray(g)?g:[g];let lt=O.reduce(i(v),{});V===!1&&(lt={});const{prevResolvedValues:qe={}}=T,Ei={...qe,...lt},Ze=F=>{S=!0,f.has(F)&&(k=!0,f.delete(F)),T.needsAnimating[F]=!0;const U=t.getValue(F);U&&(U.liveStyle=!1)};for(const F in Ei){const U=lt[F],Yt=qe[F];if(d.hasOwnProperty(F))continue;let Xt=!1;ce(U)&&ce(Yt)?Xt=!Fs(U,Yt):Xt=U!==Yt,Xt?U!=null?Ze(F):f.add(F):U!==void 0&&f.has(F)?Ze(F):T.protectedKeys[F]=!0}T.prevProp=g,T.prevResolvedValues=lt,T.isActive&&(d={...d,...lt}),s&&t.blockInitialAnimation&&(S=!1),S&&(!M||k)&&h.push(...O.map(F=>({animation:F,options:{type:v}})))}if(f.size){const y={};f.forEach(v=>{const T=t.getBaseTarget(v),g=t.getValue(v);g&&(g.liveStyle=!0),y[v]=T===void 0?null:T}),h.push({animation:y})}let p=!!h.length;return s&&(c.initial===!1||c.initial===c.animate)&&!t.manuallyAnimateOnMount&&(p=!1),s=!1,p?e(h):Promise.resolve()}function a(l,c){var u;if(n[l].isActive===c)return Promise.resolve();(u=t.variantChildren)===null||u===void 0||u.forEach(f=>{var d;return(d=f.animationState)===null||d===void 0?void 0:d.setActive(l,c)}),n[l].isActive=c;const h=r(l);for(const f in n)n[f].protectedKeys={};return h}return{animateChanges:r,setActive:a,setAnimateFunction:o,getState:()=>n}}function ba(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!Fs(e,t):!1}function nt(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Sa(){return{animate:nt(!0),whileInView:nt(),whileHover:nt(),whileTap:nt(),whileDrag:nt(),whileFocus:nt(),exit:nt()}}class Va extends et{constructor(e){super(e),e.animationState||(e.animationState=Pa(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();this.unmount(),_t(e)&&(this.unmount=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:n}=this.node.prevProps||{};e!==n&&this.updateAnimationControlsSubscription()}unmount(){}}let wa=0;class Aa extends et{constructor(){super(...arguments),this.id=wa++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:n}=this.node.presenceContext,{isPresent:s}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===s)return;const i=this.node.animationState.setActive("exit",!e);n&&!e&&i.then(()=>n(this.id))}mount(){const{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}const Ca={animation:{Feature:Va},exit:{Feature:Aa}},Vn=(t,e)=>Math.abs(t-e);function Da(t,e){const n=Vn(t.x,e.x),s=Vn(t.y,e.y);return Math.sqrt(n**2+s**2)}class fi{constructor(e,n,{transformPagePoint:s,contextWindow:i,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const h=ie(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,d=Da(h.offset,{x:0,y:0})>=3;if(!f&&!d)return;const{point:m}=h,{timestamp:p}=C;this.history.push({...m,timestamp:p});const{onStart:y,onMove:v}=this.handlers;f||(y&&y(this.lastMoveEvent,h),this.startEvent=this.lastMoveEvent),v&&v(this.lastMoveEvent,h)},this.handlePointerMove=(h,f)=>{this.lastMoveEvent=h,this.lastMoveEventInfo=se(f,this.transformPagePoint),D.update(this.updatePoint,!0)},this.handlePointerUp=(h,f)=>{this.end();const{onEnd:d,onSessionEnd:m,resumeAnimation:p}=this.handlers;if(this.dragSnapToOrigin&&p&&p(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const y=ie(h.type==="pointercancel"?this.lastMoveEventInfo:se(f,this.transformPagePoint),this.history);this.startEvent&&d&&d(h,y),m&&m(h,y)},!Ds(e))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=s,this.contextWindow=i||window;const r=$t(e),a=se(r,this.transformPagePoint),{point:l}=a,{timestamp:c}=C;this.history=[{...l,timestamp:c}];const{onSessionStart:u}=n;u&&u(e,ie(a,this.history)),this.removeListeners=G(_(this.contextWindow,"pointermove",this.handlePointerMove),_(this.contextWindow,"pointerup",this.handlePointerUp),_(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),J(this.updatePoint)}}function se(t,e){return e?{point:e(t.point)}:t}function wn(t,e){return{x:t.x-e.x,y:t.y-e.y}}function ie({point:t},e){return{point:t,delta:wn(t,di(e)),offset:wn(t,Ma(e)),velocity:Ra(e,.1)}}function Ma(t){return t[0]}function di(t){return t[t.length-1]}function Ra(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,s=null;const i=di(t);for(;n>=0&&(s=t[n],!(i.timestamp-s.timestamp>q(e)));)n--;if(!s)return{x:0,y:0};const o=$(i.timestamp-s.timestamp);if(o===0)return{x:0,y:0};const r={x:(i.x-s.x)/o,y:(i.y-s.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function B(t){return t.max-t.min}function ve(t,e=0,n=.01){return Math.abs(t-e)<=n}function An(t,e,n,s=.5){t.origin=s,t.originPoint=w(e.min,e.max,t.origin),t.scale=B(n)/B(e),(ve(t.scale,1,1e-4)||isNaN(t.scale))&&(t.scale=1),t.translate=w(n.min,n.max,t.origin)-t.originPoint,(ve(t.translate)||isNaN(t.translate))&&(t.translate=0)}function xt(t,e,n,s){An(t.x,e.x,n.x,s?s.originX:void 0),An(t.y,e.y,n.y,s?s.originY:void 0)}function Cn(t,e,n){t.min=n.min+e.min,t.max=t.min+B(e)}function Ea(t,e,n){Cn(t.x,e.x,n.x),Cn(t.y,e.y,n.y)}function Dn(t,e,n){t.min=e.min-n.min,t.max=t.min+B(e)}function Tt(t,e,n){Dn(t.x,e.x,n.x),Dn(t.y,e.y,n.y)}function La(t,{min:e,max:n},s){return e!==void 0&&t<e?t=s?w(e,t,s.min):Math.max(t,e):n!==void 0&&t>n&&(t=s?w(n,t,s.max):Math.min(t,n)),t}function Mn(t,e,n){return{min:e!==void 0?t.min+e:void 0,max:n!==void 0?t.max+n-(t.max-t.min):void 0}}function Fa(t,{top:e,left:n,bottom:s,right:i}){return{x:Mn(t.x,n,i),y:Mn(t.y,e,s)}}function Rn(t,e){let n=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,s]=[s,n]),{min:n,max:s}}function Ba(t,e){return{x:Rn(t.x,e.x),y:Rn(t.y,e.y)}}function ka(t,e){let n=.5;const s=B(t),i=B(e);return i>s?n=St(e.min,e.max-s,t.min):s>i&&(n=St(t.min,t.max-i,e.min)),Q(0,1,n)}function Oa(t,e){const n={};return e.min!==void 0&&(n.min=e.min-t.min),e.max!==void 0&&(n.max=e.max-t.min),n}const xe=.35;function ja(t=xe){return t===!1?t=0:t===!0&&(t=xe),{x:En(t,"left","right"),y:En(t,"top","bottom")}}function En(t,e,n){return{min:Ln(t,e),max:Ln(t,n)}}function Ln(t,e){return typeof t=="number"?t:t[e]||0}const Fn=()=>({translate:0,scale:1,origin:0,originPoint:0}),ht=()=>({x:Fn(),y:Fn()}),Bn=()=>({min:0,max:0}),A=()=>({x:Bn(),y:Bn()});function I(t){return[t("x"),t("y")]}function mi({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}function Ia({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function Na(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}function re(t){return t===void 0||t===1}function Te({scale:t,scaleX:e,scaleY:n}){return!re(t)||!re(e)||!re(n)}function st(t){return Te(t)||pi(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function pi(t){return kn(t.x)||kn(t.y)}function kn(t){return t&&t!=="0%"}function Kt(t,e,n){const s=t-n,i=e*s;return n+i}function On(t,e,n,s,i){return i!==void 0&&(t=Kt(t,i,s)),Kt(t,n,s)+e}function Pe(t,e=0,n=1,s,i){t.min=On(t.min,e,n,s,i),t.max=On(t.max,e,n,s,i)}function gi(t,{x:e,y:n}){Pe(t.x,e.translate,e.scale,e.originPoint),Pe(t.y,n.translate,n.scale,n.originPoint)}function Ua(t,e,n,s=!1){const i=n.length;if(!i)return;e.x=e.y=1;let o,r;for(let a=0;a<i;a++){o=n[a],r=o.projectionDelta;const l=o.instance;l&&l.style&&l.style.display==="contents"||(s&&o.options.layoutScroll&&o.scroll&&o!==o.root&&ft(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,gi(t,r)),s&&st(o.latestValues)&&ft(t,o.latestValues))}e.x=jn(e.x),e.y=jn(e.y)}function jn(t){return Number.isInteger(t)||t>1.0000000000001||t<.999999999999?t:1}function Y(t,e){t.min=t.min+e,t.max=t.max+e}function In(t,e,[n,s,i]){const o=e[i]!==void 0?e[i]:.5,r=w(t.min,t.max,o);Pe(t,e[n],e[s],r,e.scale)}const Ka=["x","scaleX","originX"],Wa=["y","scaleY","originY"];function ft(t,e){In(t.x,e,Ka),In(t.y,e,Wa)}function yi(t,e){return mi(Na(t.getBoundingClientRect(),e))}function _a(t,e,n){const s=yi(t,n),{scroll:i}=e;return i&&(Y(s.x,i.offset.x),Y(s.y,i.offset.y)),s}const vi=({current:t})=>t?t.ownerDocument.defaultView:null,Ga=new WeakMap;class $a{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=A(),this.visualElement=e}start(e,{snapToCursor:n=!1}={}){const{presenceContext:s}=this.visualElement;if(s&&s.isPresent===!1)return;const i=u=>{const{dragSnapToOrigin:h}=this.getProps();h?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor($t(u,"page").point)},o=(u,h)=>{const{drag:f,dragPropagation:d,onDragStart:m}=this.getProps();if(f&&!d&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=Rs(f),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),I(y=>{let v=this.getAxisMotionValue(y).get()||0;if(N.test(v)){const{projection:T}=this.visualElement;if(T&&T.layout){const g=T.layout.layoutBox[y];g&&(v=B(g)*(parseFloat(v)/100))}}this.originPoint[y]=v}),m&&m(u,h);const{animationState:p}=this.visualElement;p&&p.setActive("whileDrag",!0)},r=(u,h)=>{const{dragPropagation:f,dragDirectionLock:d,onDirectionLock:m,onDrag:p}=this.getProps();if(!f&&!this.openGlobalLock)return;const{offset:y}=h;if(d&&this.currentDirection===null){this.currentDirection=za(y),this.currentDirection!==null&&m&&m(this.currentDirection);return}this.updateAxis("x",h.point,y),this.updateAxis("y",h.point,y),this.visualElement.render(),p&&p(u,h)},a=(u,h)=>this.stop(u,h),l=()=>I(u=>{var h;return this.getAnimationState(u)==="paused"&&((h=this.getAxisMotionValue(u).animation)===null||h===void 0?void 0:h.play())}),{dragSnapToOrigin:c}=this.getProps();this.panSession=new fi(e,{onSessionStart:i,onStart:o,onMove:r,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:c,contextWindow:vi(this.visualElement)})}stop(e,n){const s=this.isDragging;if(this.cancel(),!s)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:o}=this.getProps();o&&o(e,n)}cancel(){this.isDragging=!1;const{projection:e,animationState:n}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:s}=this.getProps();!s&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(e,n,s){const{drag:i}=this.getProps();if(!s||!Et(e,i,this.currentDirection))return;const o=this.getAxisMotionValue(e);let r=this.originPoint[e]+s[e];this.constraints&&this.constraints[e]&&(r=La(r,this.constraints[e],this.elastic[e])),o.set(r)}resolveConstraints(){var e;const{dragConstraints:n,dragElastic:s}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(e=this.visualElement.projection)===null||e===void 0?void 0:e.layout,o=this.constraints;n&&ct(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=Fa(i.layoutBox,n):this.constraints=!1,this.elastic=ja(s),o!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&I(r=>{this.getAxisMotionValue(r)&&(this.constraints[r]=Oa(i.layoutBox[r],this.constraints[r]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!ct(e))return!1;const s=e.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=_a(s,i.root,this.visualElement.getTransformPagePoint());let r=Ba(i.layout.layoutBox,o);if(n){const a=n(Ia(r));this.hasMutatedConstraints=!!a,a&&(r=mi(a))}return r}startAnimation(e){const{drag:n,dragMomentum:s,dragElastic:i,dragTransition:o,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},c=I(u=>{if(!Et(u,n,this.currentDirection))return;let h=l&&l[u]||{};r&&(h={min:0,max:0});const f=i?200:1e6,d=i?40:1e7,m={type:"inertia",velocity:s?e[u]:0,bounceStiffness:f,bounceDamping:d,timeConstant:750,restDelta:1,restSpeed:10,...o,...h};return this.startAxisValueAnimation(u,m)});return Promise.all(c).then(a)}startAxisValueAnimation(e,n){const s=this.getAxisMotionValue(e);return s.start(ze(e,s,0,n,this.visualElement))}stopAnimation(){I(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){I(e=>{var n;return(n=this.getAxisMotionValue(e).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(e){var n;return(n=this.getAxisMotionValue(e).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(e){const n="_drag"+e.toUpperCase(),s=this.visualElement.getProps(),i=s[n];return i||this.visualElement.getValue(e,(s.initial?s.initial[e]:void 0)||0)}snapToCursor(e){I(n=>{const{drag:s}=this.getProps();if(!Et(n,s,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:r,max:a}=i.layout.layoutBox[n];o.set(e[n]-w(r,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:n}=this.getProps(),{projection:s}=this.visualElement;if(!ct(n)||!s||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};I(r=>{const a=this.getAxisMotionValue(r);if(a){const l=a.get();i[r]=ka({min:l,max:l},this.constraints[r])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",s.root&&s.root.updateScroll(),s.updateLayout(),this.resolveConstraints(),I(r=>{if(!Et(r,e,null))return;const a=this.getAxisMotionValue(r),{min:l,max:c}=this.constraints[r];a.set(w(l,c,i[r]))})}addListeners(){if(!this.visualElement.current)return;Ga.set(this.visualElement,this);const e=this.visualElement.current,n=_(e,"pointerdown",l=>{const{drag:c,dragListener:u=!0}=this.getProps();c&&u&&this.start(l)}),s=()=>{const{dragConstraints:l}=this.getProps();ct(l)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,o=i.addEventListener("measure",s);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),s();const r=K(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:l,hasLayoutChanged:c})=>{this.isDragging&&c&&(I(u=>{const h=this.getAxisMotionValue(u);h&&(this.originPoint[u]+=l[u].translate,h.set(h.get()+l[u].translate))}),this.visualElement.render())});return()=>{r(),n(),o(),a&&a()}}getProps(){const e=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:s=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:r=xe,dragMomentum:a=!0}=e;return{...e,drag:n,dragDirectionLock:s,dragPropagation:i,dragConstraints:o,dragElastic:r,dragMomentum:a}}}function Et(t,e,n){return(e===!0||e===t)&&(n===null||n===t)}function za(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}class Ha extends et{constructor(e){super(e),this.removeGroupControls=L,this.removeListeners=L,this.controls=new $a(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||L}unmount(){this.removeGroupControls(),this.removeListeners()}}const Nn=t=>(e,n)=>{t&&t(e,n)};class Ya extends et{constructor(){super(...arguments),this.removePointerDownListener=L}onPointerDown(e){this.session=new fi(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:vi(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:n,onPan:s,onPanEnd:i}=this.node.getProps();return{onSessionStart:Nn(e),onStart:Nn(n),onMove:s,onEnd:(o,r)=>{delete this.session,i&&i(o,r)}}}mount(){this.removePointerDownListener=_(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}function Xa(){const t=P.useContext(Se);if(t===null)return[!0,null];const{isPresent:e,onExitComplete:n,register:s}=t,i=P.useId();return P.useEffect(()=>s(i),[]),!e&&n?[!1,()=>n&&n(i)]:[!0]}const Bt={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Un(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const pt={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(x.test(t))t=parseFloat(t);else return t;const n=Un(t,e.target.x),s=Un(t,e.target.y);return`${n}% ${s}%`}},qa={correct:(t,{treeScale:e,projectionDelta:n})=>{const s=t,i=tt.parse(t);if(i.length>5)return s;const o=tt.createTransformer(t),r=typeof i[0]!="number"?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;i[0+r]/=a,i[1+r]/=l;const c=w(a,l,.5);return typeof i[2+r]=="number"&&(i[2+r]/=c),typeof i[3+r]=="number"&&(i[3+r]/=c),o(i)}};class Za extends W.Component{componentDidMount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s,layoutId:i}=this.props,{projection:o}=e;Xi(Qa),o&&(n.group&&n.group.add(o),s&&s.register&&i&&s.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),Bt.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:n,visualElement:s,drag:i,isPresent:o}=this.props,r=s.projection;return r&&(r.isPresent=o,i||e.layoutDependency!==n||n===void 0?r.willUpdate():this.safeToRemove(),e.isPresent!==o&&(o?r.promote():r.relegate()||D.postRender(()=>{const a=r.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),Ae.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s}=this.props,{projection:i}=e;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),s&&s.deregister&&s.deregister(i))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function xi(t){const[e,n]=Xa(),s=P.useContext(ms);return W.createElement(Za,{...t,layoutGroup:s,switchLayoutGroup:P.useContext(ps),isPresent:e,safeToRemove:n})}const Qa={borderRadius:{...pt,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:pt,borderTopRightRadius:pt,borderBottomLeftRadius:pt,borderBottomRightRadius:pt,boxShadow:qa},Ti=["TopLeft","TopRight","BottomLeft","BottomRight"],Ja=Ti.length,Kn=t=>typeof t=="string"?parseFloat(t):t,Wn=t=>typeof t=="number"||x.test(t);function tl(t,e,n,s,i,o){i?(t.opacity=w(0,n.opacity!==void 0?n.opacity:1,el(s)),t.opacityExit=w(e.opacity!==void 0?e.opacity:1,0,nl(s))):o&&(t.opacity=w(e.opacity!==void 0?e.opacity:1,n.opacity!==void 0?n.opacity:1,s));for(let r=0;r<Ja;r++){const a=`border${Ti[r]}Radius`;let l=_n(e,a),c=_n(n,a);if(l===void 0&&c===void 0)continue;l||(l=0),c||(c=0),l===0||c===0||Wn(l)===Wn(c)?(t[a]=Math.max(w(Kn(l),Kn(c),s),0),(N.test(c)||N.test(l))&&(t[a]+="%")):t[a]=c}(e.rotate||n.rotate)&&(t.rotate=w(e.rotate||0,n.rotate||0,s))}function _n(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const el=Pi(0,.5,si),nl=Pi(.5,.95,L);function Pi(t,e,n){return s=>s<t?0:s>e?1:n(St(t,e,s))}function Gn(t,e){t.min=e.min,t.max=e.max}function j(t,e){Gn(t.x,e.x),Gn(t.y,e.y)}function $n(t,e,n,s,i){return t-=e,t=Kt(t,1/n,s),i!==void 0&&(t=Kt(t,1/i,s)),t}function sl(t,e=0,n=1,s=.5,i,o=t,r=t){if(N.test(e)&&(e=parseFloat(e),e=w(r.min,r.max,e/100)-r.min),typeof e!="number")return;let a=w(o.min,o.max,s);t===o&&(a-=e),t.min=$n(t.min,e,n,a,i),t.max=$n(t.max,e,n,a,i)}function zn(t,e,[n,s,i],o,r){sl(t,e[n],e[s],e[i],e.scale,o,r)}const il=["x","scaleX","originX"],rl=["y","scaleY","originY"];function Hn(t,e,n,s){zn(t.x,e,il,n?n.x:void 0,s?s.x:void 0),zn(t.y,e,rl,n?n.y:void 0,s?s.y:void 0)}function Yn(t){return t.translate===0&&t.scale===1}function bi(t){return Yn(t.x)&&Yn(t.y)}function ol(t,e){return t.x.min===e.x.min&&t.x.max===e.x.max&&t.y.min===e.y.min&&t.y.max===e.y.max}function Si(t,e){return Math.round(t.x.min)===Math.round(e.x.min)&&Math.round(t.x.max)===Math.round(e.x.max)&&Math.round(t.y.min)===Math.round(e.y.min)&&Math.round(t.y.max)===Math.round(e.y.max)}function Xn(t){return B(t.x)/B(t.y)}class al{constructor(){this.members=[]}add(e){He(this.members,e),e.scheduleRender()}remove(e){if(Ye(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(e){const n=this.members.findIndex(i=>e===i);if(n===0)return!1;let s;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){s=o;break}}return s?(this.promote(s),!0):!1}promote(e,n){const s=this.lead;if(e!==s&&(this.prevLead=s,this.lead=e,e.show(),s)){s.instance&&s.scheduleRender(),e.scheduleRender(),e.resumeFrom=s,n&&(e.resumeFrom.preserveOpacity=!0),s.snapshot&&(e.snapshot=s.snapshot,e.snapshot.latestValues=s.animationValues||s.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:i}=e.options;i===!1&&s.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:n,resumingFrom:s}=e;n.onExitComplete&&n.onExitComplete(),s&&s.options.onExitComplete&&s.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function qn(t,e,n){let s="";const i=t.x.translate/e.x,o=t.y.translate/e.y,r=n?.z||0;if((i||o||r)&&(s=`translate3d(${i}px, ${o}px, ${r}px) `),(e.x!==1||e.y!==1)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:c,rotate:u,rotateX:h,rotateY:f,skewX:d,skewY:m}=n;c&&(s=`perspective(${c}px) ${s}`),u&&(s+=`rotate(${u}deg) `),h&&(s+=`rotateX(${h}deg) `),f&&(s+=`rotateY(${f}deg) `),d&&(s+=`skewX(${d}deg) `),m&&(s+=`skewY(${m}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return(a!==1||l!==1)&&(s+=`scale(${a}, ${l})`),s||"none"}const ll=(t,e)=>t.depth-e.depth;class cl{constructor(){this.children=[],this.isDirty=!1}add(e){He(this.children,e),this.isDirty=!0}remove(e){Ye(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(ll),this.isDirty=!1,this.children.forEach(e)}}function ul(t,e){const n=Z.now(),s=({timestamp:i})=>{const o=i-n;o>=e&&(J(s),t(o-e))};return D.read(s,!0),()=>J(s)}function hl(t){window.MotionDebug&&window.MotionDebug.record(t)}function fl(t){return t instanceof SVGElement&&t.tagName!=="svg"}function dl(t,e,n){const s=E(t)?t:Vt(t);return s.start(ze("",s,e,n)),s.animation}const oe=["","X","Y","Z"],ml={visibility:"hidden"},Zn=1e3;let pl=0;const it={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function ae(t,e,n,s){const{latestValues:i}=e;i[t]&&(n[t]=i[t],e.setStaticValue(t,0),s&&(s[t]=0))}function Vi({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:s,resetTransform:i}){return class{constructor(r={},a=e?.()){this.id=pl++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,it.totalNodes=it.resolvedTargetDeltas=it.recalculatedProjection=0,this.nodes.forEach(vl),this.nodes.forEach(Sl),this.nodes.forEach(Vl),this.nodes.forEach(xl),hl(it)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=r,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new cl)}addEventListener(r,a){return this.eventHandlers.has(r)||this.eventHandlers.set(r,new Xe),this.eventHandlers.get(r).add(a)}notifyListeners(r,...a){const l=this.eventHandlers.get(r);l&&l.notify(...a)}hasListeners(r){return this.eventHandlers.has(r)}mount(r,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=fl(r),this.instance=r;const{layoutId:l,layout:c,visualElement:u}=this.options;if(u&&!u.current&&u.mount(r),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(c||l)&&(this.isLayoutDirty=!0),t){let h;const f=()=>this.root.updateBlockedByResize=!1;t(r,()=>{this.root.updateBlockedByResize=!0,h&&h(),h=ul(f,250),Bt.hasAnimatedSinceResize&&(Bt.hasAnimatedSinceResize=!1,this.nodes.forEach(Jn))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&u&&(l||c)&&this.addEventListener("didUpdate",({delta:h,hasLayoutChanged:f,hasRelativeTargetChanged:d,layout:m})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const p=this.options.transition||u.getDefaultTransition()||Ml,{onLayoutAnimationStart:y,onLayoutAnimationComplete:v}=u.getProps(),T=!this.targetLayout||!Si(this.targetLayout,m)||d,g=!f&&d;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||g||f&&(T||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(h,g);const b={...Ie(p,"layout"),onPlay:y,onComplete:v};(u.shouldReduceMotion||this.options.layoutRoot)&&(b.delay=0,b.type=!1),this.startAnimation(b)}else f||Jn(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=m})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const r=this.getStack();r&&r.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,J(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(wl),this.animationId++)}getTransformTemplate(){const{visualElement:r}=this.options;return r&&r.getProps().transformTemplate}willUpdate(r=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let u=0;u<this.path.length;u++){const h=this.path[u];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const c=this.getTransformTemplate();this.prevTransformTemplateValue=c?c(this.latestValues,""):void 0,this.updateSnapshot(),r&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Qn);return}this.isUpdating||this.nodes.forEach(Pl),this.isUpdating=!1,window.HandoffCancelAllAnimations&&window.HandoffCancelAllAnimations(),this.nodes.forEach(bl),this.nodes.forEach(gl),this.nodes.forEach(yl),this.clearAllSnapshots();const a=Z.now();C.delta=Q(0,1e3/60,a-C.timestamp),C.timestamp=a,C.isProcessing=!0,qt.update.process(C),qt.preRender.process(C),qt.render.process(C),C.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Ae.read(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(Tl),this.sharedNodes.forEach(Al)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,D.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){D.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const r=this.layout;this.layout=this.measure(!1),this.layoutCorrected=A(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,r?r.layoutBox:void 0)}updateScroll(r="measure"){let a=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===r&&(a=!1),a&&(this.scroll={animationId:this.root.animationId,phase:r,isRoot:s(this.instance),offset:n(this.instance)})}resetTransform(){if(!i)return;const r=this.isLayoutDirty||this.shouldResetTransform,a=this.projectionDelta&&!bi(this.projectionDelta),l=this.getTransformTemplate(),c=l?l(this.latestValues,""):void 0,u=c!==this.prevTransformTemplateValue;r&&(a||st(this.latestValues)||u)&&(i(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(r=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return r&&(l=this.removeTransform(l)),Rl(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:r}=this.options;if(!r)return A();const a=r.measureViewportBox(),{scroll:l}=this.root;return l&&(Y(a.x,l.offset.x),Y(a.y,l.offset.y)),a}removeElementScroll(r){const a=A();j(a,r);for(let l=0;l<this.path.length;l++){const c=this.path[l],{scroll:u,options:h}=c;if(c!==this.root&&u&&h.layoutScroll){if(u.isRoot){j(a,r);const{scroll:f}=this.root;f&&(Y(a.x,-f.offset.x),Y(a.y,-f.offset.y))}Y(a.x,u.offset.x),Y(a.y,u.offset.y)}}return a}applyTransform(r,a=!1){const l=A();j(l,r);for(let c=0;c<this.path.length;c++){const u=this.path[c];!a&&u.options.layoutScroll&&u.scroll&&u!==u.root&&ft(l,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),st(u.latestValues)&&ft(l,u.latestValues)}return st(this.latestValues)&&ft(l,this.latestValues),l}removeTransform(r){const a=A();j(a,r);for(let l=0;l<this.path.length;l++){const c=this.path[l];if(!c.instance||!st(c.latestValues))continue;Te(c.latestValues)&&c.updateSnapshot();const u=A(),h=c.measurePageBox();j(u,h),Hn(a,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,u)}return st(this.latestValues)&&Hn(a,this.latestValues),a}setTargetDelta(r){this.targetDelta=r,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(r){this.options={...this.options,...r,crossfade:r.crossfade!==void 0?r.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==C.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(r=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const c=!!this.resumingFrom||this!==l;if(!(r||c&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget))return;const{layout:h,layoutId:f}=this.options;if(!(!this.layout||!(h||f))){if(this.resolvedRelativeTargetAt=C.timestamp,!this.targetDelta&&!this.relativeTarget){const d=this.getClosestProjectingParent();d&&d.layout&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=A(),this.relativeTargetOrigin=A(),Tt(this.relativeTargetOrigin,this.layout.layoutBox,d.layout.layoutBox),j(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=A(),this.targetWithTransforms=A()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),Ea(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):j(this.target,this.layout.layoutBox),gi(this.target,this.targetDelta)):j(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const d=this.getClosestProjectingParent();d&&!!d.resumingFrom==!!this.resumingFrom&&!d.options.layoutScroll&&d.target&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=A(),this.relativeTargetOrigin=A(),Tt(this.relativeTargetOrigin,this.target,d.target),j(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}it.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Te(this.parent.latestValues)||pi(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var r;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let c=!0;if((this.isProjectionDirty||!((r=this.parent)===null||r===void 0)&&r.isProjectionDirty)&&(c=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===C.timestamp&&(c=!1),c)return;const{layout:u,layoutId:h}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(u||h))return;j(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,d=this.treeScale.y;Ua(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=A());const{target:m}=a;if(!m){this.projectionTransform&&(this.projectionDelta=ht(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=ht(),this.projectionDeltaWithTransform=ht());const p=this.projectionTransform;xt(this.projectionDelta,this.layoutCorrected,m,this.latestValues),this.projectionTransform=qn(this.projectionDelta,this.treeScale),(this.projectionTransform!==p||this.treeScale.x!==f||this.treeScale.y!==d)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",m)),it.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(r=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),r){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(r,a=!1){const l=this.snapshot,c=l?l.latestValues:{},u={...this.latestValues},h=ht();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=A(),d=l?l.source:void 0,m=this.layout?this.layout.source:void 0,p=d!==m,y=this.getStack(),v=!y||y.members.length<=1,T=!!(p&&!v&&this.options.crossfade===!0&&!this.path.some(Dl));this.animationProgress=0;let g;this.mixTargetDelta=b=>{const V=b/1e3;ts(h.x,r.x,V),ts(h.y,r.y,V),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Tt(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Cl(this.relativeTarget,this.relativeTargetOrigin,f,V),g&&ol(this.relativeTarget,g)&&(this.isProjectionDirty=!1),g||(g=A()),j(g,this.relativeTarget)),p&&(this.animationValues=u,tl(u,c,this.latestValues,V,T,v)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=V},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(r){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(J(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=D.update(()=>{Bt.hasAnimatedSinceResize=!0,this.currentAnimation=dl(0,Zn,{...r,onUpdate:a=>{this.mixTargetDelta(a),r.onUpdate&&r.onUpdate(a)},onComplete:()=>{r.onComplete&&r.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const r=this.getStack();r&&r.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Zn),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const r=this.getLead();let{targetWithTransforms:a,target:l,layout:c,latestValues:u}=r;if(!(!a||!l||!c)){if(this!==r&&this.layout&&c&&wi(this.options.animationType,this.layout.layoutBox,c.layoutBox)){l=this.target||A();const h=B(this.layout.layoutBox.x);l.x.min=r.target.x.min,l.x.max=l.x.min+h;const f=B(this.layout.layoutBox.y);l.y.min=r.target.y.min,l.y.max=l.y.min+f}j(a,l),ft(a,u),xt(this.projectionDeltaWithTransform,this.layoutCorrected,a,u)}}registerSharedNode(r,a){this.sharedNodes.has(r)||this.sharedNodes.set(r,new al),this.sharedNodes.get(r).add(a);const c=a.options.initialPromotionConfig;a.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(a):void 0})}isLead(){const r=this.getStack();return r?r.lead===this:!0}getLead(){var r;const{layoutId:a}=this.options;return a?((r=this.getStack())===null||r===void 0?void 0:r.lead)||this:this}getPrevLead(){var r;const{layoutId:a}=this.options;return a?(r=this.getStack())===null||r===void 0?void 0:r.prevLead:void 0}getStack(){const{layoutId:r}=this.options;if(r)return this.root.sharedNodes.get(r)}promote({needsReset:r,transition:a,preserveFollowOpacity:l}={}){const c=this.getStack();c&&c.promote(this,l),r&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const r=this.getStack();return r?r.relegate(this):!1}resetSkewAndRotation(){const{visualElement:r}=this.options;if(!r)return;let a=!1;const{latestValues:l}=r;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const c={};l.z&&ae("z",r,c,this.animationValues);for(let u=0;u<oe.length;u++)ae(`rotate${oe[u]}`,r,c,this.animationValues),ae(`skew${oe[u]}`,r,c,this.animationValues);r.render();for(const u in c)r.setStaticValue(u,c[u]),this.animationValues&&(this.animationValues[u]=c[u]);r.scheduleRender()}getProjectionStyles(r){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return ml;const c={visibility:""},u=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,c.opacity="",c.pointerEvents=Lt(r?.pointerEvents)||"",c.transform=u?u(this.latestValues,""):"none",c;const h=this.getLead();if(!this.projectionDelta||!this.layout||!h.target){const p={};return this.options.layoutId&&(p.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,p.pointerEvents=Lt(r?.pointerEvents)||""),this.hasProjected&&!st(this.latestValues)&&(p.transform=u?u({},""):"none",this.hasProjected=!1),p}const f=h.animationValues||h.latestValues;this.applyTransformsToTarget(),c.transform=qn(this.projectionDeltaWithTransform,this.treeScale,f),u&&(c.transform=u(f,c.transform));const{x:d,y:m}=this.projectionDelta;c.transformOrigin=`${d.origin*100}% ${m.origin*100}% 0`,h.animationValues?c.opacity=h===this?(l=(a=f.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:c.opacity=h===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const p in kt){if(f[p]===void 0)continue;const{correct:y,applyTo:v}=kt[p],T=c.transform==="none"?f[p]:y(f[p],h);if(v){const g=v.length;for(let b=0;b<g;b++)c[v[b]]=T}else c[p]=T}return this.options.layoutId&&(c.pointerEvents=h===this?Lt(r?.pointerEvents)||"":"none"),c}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(r=>{var a;return(a=r.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(Qn),this.root.sharedNodes.clear()}}}function gl(t){t.updateLayout()}function yl(t){var e;const n=((e=t.resumeFrom)===null||e===void 0?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&n&&t.hasListeners("didUpdate")){const{layoutBox:s,measuredBox:i}=t.layout,{animationType:o}=t.options,r=n.source!==t.layout.source;o==="size"?I(h=>{const f=r?n.measuredBox[h]:n.layoutBox[h],d=B(f);f.min=s[h].min,f.max=f.min+d}):wi(o,n.layoutBox,s)&&I(h=>{const f=r?n.measuredBox[h]:n.layoutBox[h],d=B(s[h]);f.max=f.min+d,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[h].max=t.relativeTarget[h].min+d)});const a=ht();xt(a,s,n.layoutBox);const l=ht();r?xt(l,t.applyTransform(i,!0),n.measuredBox):xt(l,s,n.layoutBox);const c=!bi(a);let u=!1;if(!t.resumeFrom){const h=t.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:f,layout:d}=h;if(f&&d){const m=A();Tt(m,n.layoutBox,f.layoutBox);const p=A();Tt(p,s,d.layoutBox),Si(m,p)||(u=!0),h.options.layoutRoot&&(t.relativeTarget=p,t.relativeTargetOrigin=m,t.relativeParent=h)}}}t.notifyListeners("didUpdate",{layout:s,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:c,hasRelativeTargetChanged:u})}else if(t.isLead()){const{onExitComplete:s}=t.options;s&&s()}t.options.transition=void 0}function vl(t){it.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function xl(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function Tl(t){t.clearSnapshot()}function Qn(t){t.clearMeasurements()}function Pl(t){t.isLayoutDirty=!1}function bl(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function Jn(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function Sl(t){t.resolveTargetDelta()}function Vl(t){t.calcProjection()}function wl(t){t.resetSkewAndRotation()}function Al(t){t.removeLeadSnapshot()}function ts(t,e,n){t.translate=w(e.translate,0,n),t.scale=w(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function es(t,e,n,s){t.min=w(e.min,n.min,s),t.max=w(e.max,n.max,s)}function Cl(t,e,n,s){es(t.x,e.x,n.x,s),es(t.y,e.y,n.y,s)}function Dl(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const Ml={duration:.45,ease:[.4,0,.1,1]},ns=t=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),ss=ns("applewebkit/")&&!ns("chrome/")?Math.round:L;function is(t){t.min=ss(t.min),t.max=ss(t.max)}function Rl(t){is(t.x),is(t.y)}function wi(t,e,n){return t==="position"||t==="preserve-aspect"&&!ve(Xn(e),Xn(n),.2)}const El=Vi({attachResizeListener:(t,e)=>K(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),le={current:void 0},Ai=Vi({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!le.current){const t=new El({});t.mount(window),t.setOptions({layoutScroll:!0}),le.current=t}return le.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),Ll={pan:{Feature:Ya},drag:{Feature:Ha,ProjectionNode:Ai,MeasureLayout:xi}},be={current:null},Ci={current:!1};function Fl(){if(Ci.current=!0,!!Ve)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>be.current=t.matches;t.addListener(e),e()}else be.current=!1}function Bl(t,e,n){const{willChange:s}=e;for(const i in e){const o=e[i],r=n[i];if(E(o))t.addValue(i,o),Ut(s)&&s.add(i);else if(E(r))t.addValue(i,Vt(o,{owner:t})),Ut(s)&&s.remove(i);else if(r!==o)if(t.hasValue(i)){const a=t.getValue(i);a.liveStyle===!0?a.jump(o):a.hasAnimated||a.set(o)}else{const a=t.getStaticValue(i);t.addValue(i,Vt(a!==void 0?a:o,{owner:t}))}}for(const i in n)e[i]===void 0&&t.removeValue(i);return e}const rs=new WeakMap,kl=[...Ns,R,tt],Ol=t=>kl.find(Is(t)),Di=Object.keys(bt),jl=Di.length,os=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],Il=De.length;function Mi(t){if(t)return t.options.allowProjection!==!1?t.projection:Mi(t.parent)}class Nl{constructor({parent:e,props:n,presenceContext:s,reducedMotionConfig:i,blockInitialAnimation:o,visualState:r},a={}){this.resolveKeyframes=(f,d,m,p)=>new this.KeyframeResolver(f,d,m,p,this),this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Ne,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>D.render(this.render,!1,!0);const{latestValues:l,renderState:c}=r;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=c,this.parent=e,this.props=n,this.presenceContext=s,this.depth=e?e.depth+1:0,this.reducedMotionConfig=i,this.options=a,this.blockInitialAnimation=!!o,this.isControllingVariants=Gt(n),this.isVariantNode=ds(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:u,...h}=this.scrapeMotionValuesFromProps(n,{},this);for(const f in h){const d=h[f];l[f]!==void 0&&E(d)&&(d.set(l[f],!1),Ut(u)&&u.add(f))}}scrapeMotionValuesFromProps(e,n,s){return{}}mount(e){this.current=e,rs.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,s)=>this.bindToMotionValue(s,n)),Ci.current||Fl(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:be.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){rs.delete(this.current),this.projection&&this.projection.unmount(),J(this.notifyUpdate),J(this.render),this.valueSubscriptions.forEach(e=>e()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features)this.features[e].unmount();this.current=null}bindToMotionValue(e,n){const s=at.has(e),i=n.on("change",r=>{this.latestValues[e]=r,this.props.onUpdate&&D.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,()=>{i(),o(),n.owner&&n.stop()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}loadFeatures({children:e,...n},s,i,o){let r,a;for(let l=0;l<jl;l++){const c=Di[l],{isEnabled:u,Feature:h,ProjectionNode:f,MeasureLayout:d}=bt[c];f&&(r=f),u(n)&&(!this.features[c]&&h&&(this.features[c]=new h(this)),d&&(a=d))}if((this.type==="html"||this.type==="svg")&&!this.projection&&r){this.projection=new r(this.latestValues,Mi(this.parent));const{layoutId:l,layout:c,drag:u,dragConstraints:h,layoutScroll:f,layoutRoot:d}=n;this.projection.setOptions({layoutId:l,layout:c,alwaysMeasureLayout:!!u||h&&ct(h),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof c=="string"?c:"both",initialPromotionConfig:o,layoutScroll:f,layoutRoot:d})}return a}updateFeatures(){for(const e in this.features){const n=this.features[e];n.isMounted?n.update():(n.mount(),n.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):A()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}update(e,n){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let s=0;s<os.length;s++){const i=os[s];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const o=e["on"+i];o&&(this.propEventSubscriptions[i]=this.on(i,o))}this.prevMotionValues=Bl(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(e=!1){if(e)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const s=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(s.initial=this.props.initial),s}const n={};for(let s=0;s<Il;s++){const i=De[s],o=this.props[i];(Pt(o)||o===!1)&&(n[i]=o)}return n}addVariantChild(e){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(e),()=>n.variantChildren.delete(e)}addValue(e,n){n!==this.values.get(e)&&(this.removeValue(e),this.bindToMotionValue(e,n)),this.values.set(e,n),this.latestValues[e]=n.get()}removeValue(e){this.values.delete(e);const n=this.valueSubscriptions.get(e);n&&(n(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return s===void 0&&n!==void 0&&(s=Vt(n===null?void 0:n,{owner:this}),this.addValue(e,s)),s}readValue(e,n){var s;let i=this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:(s=this.getBaseTargetFromProps(this.props,e))!==null&&s!==void 0?s:this.readValueFromInstance(this.current,e,this.options);return i!=null&&(typeof i=="string"&&(Os(i)||Bs(i))?i=parseFloat(i):!Ol(i)&&tt.test(n)&&(i=Hs(e,n)),this.setBaseTarget(e,E(i)?i.get():i)),E(i)?i.get():i}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){var n,s;const{initial:i}=this.props,o=typeof i=="string"||typeof i=="object"?(s=je(this.props,i,(n=this.presenceContext)===null||n===void 0?void 0:n.custom))===null||s===void 0?void 0:s[e]:void 0;if(i&&o!==void 0)return o;const r=this.getBaseTargetFromProps(this.props,e);return r!==void 0&&!E(r)?r:this.initialValues[e]!==void 0&&o===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new Xe),this.events[e].add(n)}notify(e,...n){this.events[e]&&this.events[e].notify(...n)}}class Ri extends Nl{constructor(){super(...arguments),this.KeyframeResolver=Ys}sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){return e.style?e.style[n]:void 0}removeValueFromRenderState(e,{vars:n,style:s}){delete n[e],delete s[e]}}function Ul(t){return window.getComputedStyle(t)}class Kl extends Ri{constructor(){super(...arguments),this.type="html"}readValueFromInstance(e,n){if(at.has(n)){const s=Ke(n);return s&&s.default||0}else{const s=Ul(e),i=(vs(n)?s.getPropertyValue(n):s[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(e,{transformPagePoint:n}){return yi(e,n)}build(e,n,s,i){Le(e,n,s,i.transformTemplate)}scrapeMotionValuesFromProps(e,n,s){return Oe(e,n,s)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;E(e)&&(this.childSubscription=e.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}renderInstance(e,n,s,i){Ss(e,n,s,i)}}class Wl extends Ri{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){if(at.has(n)){const s=Ke(n);return s&&s.default||0}return n=Vs.has(n)?n:we(n),e.getAttribute(n)}measureInstanceViewportBox(){return A()}scrapeMotionValuesFromProps(e,n,s){return As(e,n,s)}build(e,n,s,i){Be(e,n,s,this.isSVGTag,i.transformTemplate)}renderInstance(e,n,s,i){ws(e,n,s,i)}mount(e){this.isSVGTag=ke(e.tagName),super.mount(e)}}const _l=(t,e)=>Me(t)?new Wl(e,{enableHardwareAcceleration:!1}):new Kl(e,{allowProjection:t!==P.Fragment,enableHardwareAcceleration:!0}),Gl={layout:{ProjectionNode:Ai,MeasureLayout:xi}},$l={...Ca,...Ir,...Ll,...Gl},Xl=Hi((t,e)=>wr(t,e,$l,_l));export{Hl as G,Xl as m};
