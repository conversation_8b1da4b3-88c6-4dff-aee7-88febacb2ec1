from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>
from .views import ItemTypeViewSet, LoanTermViewSet, LoanViewSet, LoanCalculationViewSet

router = DefaultRouter()
router.register('item-types', ItemTypeViewSet)
router.register('loan-terms', LoanTermViewSet)
router.register('my-loans', LoanViewSet, basename='my-loans')
router.register('calculator', LoanCalculationViewSet, basename='loan-calculator')

urlpatterns = [
    path('', include(router.urls)),
]