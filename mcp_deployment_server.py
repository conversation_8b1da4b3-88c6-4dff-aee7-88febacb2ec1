#!/usr/bin/env python3
"""
MCP Local Server for SMSMali PythonAnywhere Deployment
=====================================================

This MCP server provides tools for deploying SMSMali from Git to PythonAnywhere.

Tools provided:
- deploy_from_git: Clone/pull from Git and deploy to PythonAnywhere
- check_deployment_status: Check current deployment status
- update_webapp_config: Update PythonAnywhere web app configuration
- run_django_commands: Execute Django management commands
- check_git_status: Check Git repository status

Usage:
    python mcp_deployment_server.py

Author: SMSMali Development Team
"""

import asyncio
import json
import logging
import subprocess
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional

# MCP imports
try:
    from mcp.server import Server
    from mcp.server.models import InitializationOptions
    from mcp.server.stdio import stdio_server
    from mcp.types import (
        CallToolRequest,
        CallToolResult,
        ListToolsRequest,
        ListToolsResult,
        <PERSON><PERSON>,
        TextContent,
    )
except ImportError:
    print("❌ MCP not installed. Install with: pip install mcp")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("smsmali-deployment-server")

class SMSMaliDeploymentServer:
    def __init__(self):
        self.server = Server("smsmali-deployment")
        self.setup_tools()
    
    def setup_tools(self):
        """Register all deployment tools"""
        
        @self.server.list_tools()
        async def list_tools() -> ListToolsResult:
            return ListToolsResult(
                tools=[
                    Tool(
                        name="deploy_from_git",
                        description="Deploy SMSMali from Git repository to PythonAnywhere",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "git_url": {
                                    "type": "string",
                                    "description": "Git repository URL",
                                    "default": "https://github.com/CooperSystems-commits/SMSMali.git"
                                },
                                "target_directory": {
                                    "type": "string",
                                    "description": "Target deployment directory",
                                    "default": "/home/<USER>/SMSMali"
                                },
                                "production": {
                                    "type": "boolean",
                                    "description": "Deploy in production mode",
                                    "default": True
                                },
                                "force_clean": {
                                    "type": "boolean",
                                    "description": "Force clean deployment (removes existing directory)",
                                    "default": False
                                }
                            },
                            "required": []
                        }
                    ),
                    Tool(
                        name="check_deployment_status",
                        description="Check current deployment status and configuration",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "directory": {
                                    "type": "string",
                                    "description": "Project directory to check",
                                    "default": "/home/<USER>/SMSMali"
                                }
                            },
                            "required": []
                        }
                    ),
                    Tool(
                        name="update_webapp_config",
                        description="Generate PythonAnywhere web app configuration",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "project_directory": {
                                    "type": "string",
                                    "description": "Project directory path",
                                    "default": "/home/<USER>/SMSMali"
                                },
                                "domain": {
                                    "type": "string",
                                    "description": "Domain name",
                                    "default": "smsmali.pythonanywhere.com"
                                }
                            },
                            "required": []
                        }
                    ),
                    Tool(
                        name="run_django_commands",
                        description="Execute Django management commands",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "commands": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "Django commands to run",
                                    "default": ["collectstatic --noinput", "migrate"]
                                },
                                "directory": {
                                    "type": "string",
                                    "description": "Project directory",
                                    "default": "/home/<USER>/SMSMali"
                                }
                            },
                            "required": []
                        }
                    ),
                    Tool(
                        name="check_git_status",
                        description="Check Git repository status and authentication",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "directory": {
                                    "type": "string",
                                    "description": "Repository directory",
                                    "default": "/home/<USER>/SMSMali"
                                }
                            },
                            "required": []
                        }
                    )
                ]
            )
        
        @self.server.call_tool()
        async def call_tool(name: str, arguments: Dict[str, Any]) -> CallToolResult:
            try:
                if name == "deploy_from_git":
                    return await self.deploy_from_git(**arguments)
                elif name == "check_deployment_status":
                    return await self.check_deployment_status(**arguments)
                elif name == "update_webapp_config":
                    return await self.update_webapp_config(**arguments)
                elif name == "run_django_commands":
                    return await self.run_django_commands(**arguments)
                elif name == "check_git_status":
                    return await self.check_git_status(**arguments)
                else:
                    return CallToolResult(
                        content=[TextContent(type="text", text=f"Unknown tool: {name}")]
                    )
            except Exception as e:
                logger.error(f"Error in tool {name}: {e}")
                return CallToolResult(
                    content=[TextContent(type="text", text=f"Error: {str(e)}")]
                )
    
    async def run_command(self, command: str, cwd: Optional[str] = None) -> Dict[str, Any]:
        """Run a shell command asynchronously"""
        try:
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=cwd
            )
            
            stdout, stderr = await process.communicate()
            
            return {
                "success": process.returncode == 0,
                "returncode": process.returncode,
                "stdout": stdout.decode('utf-8', errors='ignore'),
                "stderr": stderr.decode('utf-8', errors='ignore'),
                "command": command
            }
        except Exception as e:
            return {
                "success": False,
                "returncode": -1,
                "stdout": "",
                "stderr": str(e),
                "command": command
            }
    
    async def deploy_from_git(
        self,
        git_url: str = "https://github.com/CooperSystems-commits/SMSMali.git",
        target_directory: str = "/home/<USER>/SMSMali",
        production: bool = True,
        force_clean: bool = False
    ) -> CallToolResult:
        """Deploy SMSMali from Git repository"""
        
        results = []
        target_path = Path(target_directory)
        
        # Step 1: Handle existing directory
        if target_path.exists():
            if force_clean:
                results.append("🗑️ Removing existing directory...")
                rm_result = await self.run_command(f"rm -rf {target_directory}")
                if not rm_result["success"]:
                    return CallToolResult(
                        content=[TextContent(type="text", text=f"❌ Failed to remove directory: {rm_result['stderr']}")]
                    )
                results.append("✅ Directory removed")
            else:
                # Try to pull updates
                results.append("🔄 Updating existing repository...")
                pull_result = await self.run_command("git pull origin master", cwd=target_directory)
                if pull_result["success"]:
                    results.append("✅ Repository updated")
                else:
                    results.append(f"⚠️ Git pull failed: {pull_result['stderr']}")
                    results.append("🔄 Trying fresh clone...")
                    rm_result = await self.run_command(f"rm -rf {target_directory}")
                    if not rm_result["success"]:
                        return CallToolResult(
                            content=[TextContent(type="text", text=f"❌ Failed to remove directory: {rm_result['stderr']}")]
                        )
        
        # Step 2: Clone repository if needed
        if not target_path.exists():
            results.append("📥 Cloning repository...")
            clone_result = await self.run_command(f"git clone {git_url} {target_directory}")
            if not clone_result["success"]:
                return CallToolResult(
                    content=[TextContent(type="text", text=f"❌ Git clone failed: {clone_result['stderr']}")]
                )
            results.append("✅ Repository cloned")
        
        # Step 3: Run deployment script
        results.append("🚀 Running deployment script...")
        deploy_script = "deploy_pythonanywhere_fixed.py"
        deploy_cmd = f"python {deploy_script}"
        if production:
            deploy_cmd += " --production"
        
        deploy_result = await self.run_command(deploy_cmd, cwd=target_directory)
        if deploy_result["success"]:
            results.append("✅ Deployment completed successfully")
            results.append(f"📋 Output: {deploy_result['stdout']}")
        else:
            results.append(f"❌ Deployment failed: {deploy_result['stderr']}")
            results.append(f"📋 Output: {deploy_result['stdout']}")
        
        return CallToolResult(
            content=[TextContent(type="text", text="\n".join(results))]
        )
    
    async def check_deployment_status(self, directory: str = "/home/<USER>/SMSMali") -> CallToolResult:
        """Check current deployment status"""
        
        results = []
        dir_path = Path(directory)
        
        # Check if directory exists
        if not dir_path.exists():
            return CallToolResult(
                content=[TextContent(type="text", text=f"❌ Directory not found: {directory}")]
            )
        
        results.append(f"📁 Checking deployment status for: {directory}")
        
        # Check Django project structure
        manage_py = dir_path / "manage.py"
        settings_dir = dir_path / "smsmali"
        venv_dir = dir_path / "venv"
        wsgi_file = dir_path / "wsgi.py"
        
        results.append("\n🔍 Project Structure:")
        results.append(f"   manage.py: {'✅' if manage_py.exists() else '❌'}")
        results.append(f"   smsmali/: {'✅' if settings_dir.exists() else '❌'}")
        results.append(f"   venv/: {'✅' if venv_dir.exists() else '❌'}")
        results.append(f"   wsgi.py: {'✅' if wsgi_file.exists() else '❌'}")
        
        # Check Git status
        git_result = await self.run_command("git status --porcelain", cwd=directory)
        if git_result["success"]:
            if git_result["stdout"].strip():
                results.append(f"\n📝 Git Status: Uncommitted changes")
                results.append(f"   {git_result['stdout']}")
            else:
                results.append(f"\n📝 Git Status: Clean working tree")
        
        # Check virtual environment
        if venv_dir.exists():
            pip_list = await self.run_command(f"{venv_dir}/bin/pip list", cwd=directory)
            if pip_list["success"]:
                package_count = len(pip_list["stdout"].strip().split('\n')) - 2  # Exclude header
                results.append(f"\n📦 Virtual Environment: {package_count} packages installed")
        
        return CallToolResult(
            content=[TextContent(type="text", text="\n".join(results))]
        )
    
    async def update_webapp_config(
        self,
        project_directory: str = "/home/<USER>/SMSMali",
        domain: str = "smsmali.pythonanywhere.com"
    ) -> CallToolResult:
        """Generate PythonAnywhere web app configuration"""
        
        config = {
            "web_app_settings": {
                "domain": domain,
                "source_code": project_directory,
                "wsgi_file": f"{project_directory}/wsgi.py",
                "virtualenv": f"{project_directory}/venv"
            },
            "static_files": [
                {"url": "/static/", "directory": f"{project_directory}/staticfiles"},
                {"url": "/media/", "directory": f"{project_directory}/media"}
            ],
            "environment_variables": {
                "DJANGO_ENV": "production",
                "DJANGO_SECRET_KEY": "your-secret-key-here"
            }
        }
        
        config_text = f"""
🌐 PythonAnywhere Web App Configuration
=====================================

📋 Web App Settings:
   • Domain: {domain}
   • Source Code: {project_directory}
   • WSGI File: {project_directory}/wsgi.py
   • Virtual Environment: {project_directory}/venv

📁 Static Files Mapping:
   • /static/ → {project_directory}/staticfiles
   • /media/ → {project_directory}/media

🔧 Environment Variables:
   • DJANGO_ENV = production
   • DJANGO_SECRET_KEY = your-secret-key-here

📋 Configuration JSON:
{json.dumps(config, indent=2)}
"""
        
        return CallToolResult(
            content=[TextContent(type="text", text=config_text)]
        )
    
    async def run_django_commands(
        self,
        commands: List[str] = None,
        directory: str = "/home/<USER>/SMSMali"
    ) -> CallToolResult:
        """Execute Django management commands"""
        
        if commands is None:
            commands = ["collectstatic --noinput", "migrate"]
        
        results = []
        results.append(f"🐍 Running Django commands in: {directory}")
        
        venv_python = f"{directory}/venv/bin/python"
        manage_py = f"{directory}/manage.py"
        
        for cmd in commands:
            results.append(f"\n⚡ Running: python manage.py {cmd}")
            full_cmd = f"{venv_python} {manage_py} {cmd}"
            
            cmd_result = await self.run_command(full_cmd, cwd=directory)
            if cmd_result["success"]:
                results.append(f"✅ Command completed")
                if cmd_result["stdout"]:
                    results.append(f"📋 Output: {cmd_result['stdout']}")
            else:
                results.append(f"❌ Command failed: {cmd_result['stderr']}")
        
        return CallToolResult(
            content=[TextContent(type="text", text="\n".join(results))]
        )
    
    async def check_git_status(self, directory: str = "/home/<USER>/SMSMali") -> CallToolResult:
        """Check Git repository status and authentication"""
        
        results = []
        results.append(f"📋 Git Status for: {directory}")
        
        # Check if it's a git repository
        git_check = await self.run_command("git rev-parse --is-inside-work-tree", cwd=directory)
        if not git_check["success"]:
            return CallToolResult(
                content=[TextContent(type="text", text=f"❌ Not a Git repository: {directory}")]
            )
        
        # Get repository info
        remote_result = await self.run_command("git remote -v", cwd=directory)
        if remote_result["success"]:
            results.append(f"\n📡 Remote URLs:")
            results.append(f"   {remote_result['stdout']}")
        
        # Get current branch and status
        branch_result = await self.run_command("git branch --show-current", cwd=directory)
        if branch_result["success"]:
            results.append(f"\n🌿 Current Branch: {branch_result['stdout'].strip()}")
        
        status_result = await self.run_command("git status --porcelain", cwd=directory)
        if status_result["success"]:
            if status_result["stdout"].strip():
                results.append(f"\n📝 Working Tree: Has uncommitted changes")
                results.append(f"   {status_result['stdout']}")
            else:
                results.append(f"\n📝 Working Tree: Clean")
        
        # Test connectivity
        fetch_result = await self.run_command("git fetch --dry-run", cwd=directory)
        if fetch_result["success"]:
            results.append(f"\n🔗 Connectivity: ✅ Can connect to remote")
        else:
            results.append(f"\n🔗 Connectivity: ❌ Cannot connect to remote")
            results.append(f"   Error: {fetch_result['stderr']}")
        
        return CallToolResult(
            content=[TextContent(type="text", text="\n".join(results))]
        )

async def main():
    """Main server function"""
    deployment_server = SMSMaliDeploymentServer()
    
    async with stdio_server() as (read_stream, write_stream):
        await deployment_server.server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="smsmali-deployment",
                server_version="1.0.0",
                capabilities=deployment_server.server.get_capabilities(
                    notification_options=None,
                    experimental_capabilities=None,
                ),
            ),
        )

if __name__ == "__main__":
    asyncio.run(main())
