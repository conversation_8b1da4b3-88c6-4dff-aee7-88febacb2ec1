@echo off
echo Starting SMSMali Development Servers...
echo.

cd /d "c:\Users\<USER>\Documents\augment-projects\smsmali"

echo Activating virtual environment...
call .\django_venv\Scripts\activate.bat

echo.
echo Starting Django server in background...
start "Django Server" cmd /c "python manage.py runserver"

echo.
echo Starting React server in background...
start "React Server" cmd /c "cd frontend && npm run dev"

echo.
echo ================================================================
echo   Development servers are starting...
echo   Django API: http://127.0.0.1:8000/
echo   React Frontend: http://localhost:3000/static/frontend/
echo.
echo   Close this window or press Ctrl+C to stop servers
echo ================================================================
echo.

pause