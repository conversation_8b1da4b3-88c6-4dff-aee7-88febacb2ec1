#!/usr/bin/env python3
"""
SMSMali Deployment Script for PythonAnywhere
============================================

This script automates the deployment of the SMSMali Django + React application
to PythonAnywhere hosting service.

Prerequisites:
1. PythonAnywhere account (smsmali.pythonanywhere.com)
2. Git repository with your code
3. Node.js available for building React frontend

Usage:
    python deploy_to_pythonanywhere.py [--production]

Author: SMSMali Development Team
"""

import os
import sys
import subprocess
import shutil
import json
from pathlib import Path
import argparse
from datetime import datetime

class PythonAnywhereDeployer:
    def __init__(self, production=False):
        self.production = production
        self.project_name = "smsmali"
        self.domain = f"{self.project_name}.pythonanywhere.com"
        self.base_dir = Path(__file__).resolve().parent
        self.frontend_dir = self.base_dir / "frontend"
        self.static_dir = self.base_dir / "static"
        self.staticfiles_dir = self.base_dir / "staticfiles"
        
        # PythonAnywhere specific paths
        self.pa_home = Path.home()
        self.pa_project_dir = self.pa_home / self.project_name
        self.pa_static_dir = self.pa_home / f"{self.project_name}.pythonanywhere.com" / "static"
        
        print(f"🚀 SMSMali Deployment Script")
        print(f"📍 Target: {self.domain}")
        print(f"🏠 Project Directory: {self.pa_project_dir}")
        print(f"🌍 Environment: {'Production' if production else 'Development'}")
        print("-" * 50)

    def run_command(self, command, description, cwd=None, check=True):
        """Execute a shell command with error handling"""
        print(f"⚡ {description}...")
        try:
            if isinstance(command, str):
                result = subprocess.run(command, shell=True, cwd=cwd, check=check, 
                                      capture_output=True, text=True)
            else:
                result = subprocess.run(command, cwd=cwd, check=check, 
                                      capture_output=True, text=True)
            
            if result.stdout:
                print(f"   ✅ {result.stdout.strip()}")
            return result
        except subprocess.CalledProcessError as e:
            print(f"   ❌ Error: {e}")
            if e.stderr:
                print(f"   📝 Details: {e.stderr}")
            if check:
                sys.exit(1)
            return e

    def check_prerequisites(self):
        """Check if all required tools are available"""
        print("🔍 Checking prerequisites...")
        
        # Check if we're on PythonAnywhere
        if not os.path.exists('/home'):
            print("   ⚠️  Warning: This script is designed for PythonAnywhere")
        
        # Check Python version
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
            print(f"   ❌ Python 3.8+ required, found {python_version.major}.{python_version.minor}")
            sys.exit(1)
        print(f"   ✅ Python {python_version.major}.{python_version.minor} found")
        
        # Check if Node.js is available for frontend build
        try:
            result = subprocess.run(['node', '--version'], capture_output=True, text=True)
            print(f"   ✅ Node.js {result.stdout.strip()} found")
        except FileNotFoundError:
            print("   ⚠️  Node.js not found - frontend build will be skipped")
            return False
        
        return True

    def setup_virtual_environment(self):
        """Create and setup Python virtual environment"""
        venv_path = self.pa_project_dir / "venv"
        
        if venv_path.exists():
            print("   ♻️  Virtual environment already exists")
        else:
            self.run_command(
                f"python3 -m venv {venv_path}",
                "Creating virtual environment"
            )
        
        # Install requirements
        pip_path = venv_path / "bin" / "pip"
        requirements_file = self.base_dir / "requirements.txt"
        
        if requirements_file.exists():
            self.run_command(
                f"{pip_path} install -r {requirements_file}",
                "Installing Python dependencies"
            )
        else:
            print("   ⚠️  requirements.txt not found")

    def build_frontend(self):
        """Build React frontend for production"""
        if not self.frontend_dir.exists():
            print("   ⚠️  Frontend directory not found, skipping frontend build")
            return
        
        # Check if package.json exists
        package_json = self.frontend_dir / "package.json"
        if not package_json.exists():
            print("   ⚠️  package.json not found, skipping frontend build")
            return
        
        # Install npm dependencies
        self.run_command(
            "npm install",
            "Installing Node.js dependencies",
            cwd=self.frontend_dir
        )
        
        # Build for production
        self.run_command(
            "npm run build",
            "Building React frontend for production",
            cwd=self.frontend_dir
        )
        
        # Copy built files to Django static directory
        dist_dir = self.frontend_dir / "dist"
        if dist_dir.exists():
            # Create static/frontend directory
            frontend_static_dir = self.static_dir / "frontend"
            frontend_static_dir.mkdir(parents=True, exist_ok=True)
            
            # Copy built files
            self.run_command(
                f"cp -r {dist_dir}/* {frontend_static_dir}/",
                "Copying frontend build to static directory"
            )
        else:
            print("   ⚠️  Frontend build directory not found")

    def setup_django_settings(self):
        """Create production settings for Django"""
        settings_file = self.base_dir / "smsmali" / "settings_production.py"
        
        production_settings = '''# Production settings for PythonAnywhere
import os
from .settings import *

# Security settings
DEBUG = False
ALLOWED_HOSTS = ['smsmali.pythonanywhere.com', 'www.smsmali.pythonanywhere.com']

# Secret key from environment variable
SECRET_KEY = os.environ.get('DJANGO_SECRET_KEY', 'your-secret-key-here')

# Database configuration for PythonAnywhere
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': '/home/<USER>/smsmali/db.sqlite3',
    }
}

# Static files configuration
STATIC_URL = '/static/'
STATIC_ROOT = '/home/<USER>/smsmali/staticfiles'
STATICFILES_DIRS = [
    '/home/<USER>/smsmali/static',
]

# Media files configuration
MEDIA_URL = '/media/'
MEDIA_ROOT = '/home/<USER>/smsmali/media'

# CORS settings for production
CORS_ALLOWED_ORIGINS = [
    "https://smsmali.pythonanywhere.com",
    "https://www.smsmali.pythonanywhere.com",
]

# Security settings
SECURE_SSL_REDIRECT = True
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True

# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': '/home/<USER>/smsmali/django.log',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
'''
        
        with open(settings_file, 'w') as f:
            f.write(production_settings)
        
        print(f"   ✅ Created production settings: {settings_file}")

    def collect_static_files(self):
        """Collect Django static files"""
        manage_py = self.base_dir / "manage.py"
        venv_python = self.pa_project_dir / "venv" / "bin" / "python"
        
        if self.production:
            settings_module = "smsmali.settings_production"
        else:
            settings_module = "smsmali.settings"
        
        self.run_command(
            f"{venv_python} {manage_py} collectstatic --noinput --settings={settings_module}",
            "Collecting static files"
        )

    def run_migrations(self):
        """Run Django database migrations"""
        manage_py = self.base_dir / "manage.py"
        venv_python = self.pa_project_dir / "venv" / "bin" / "python"
        
        if self.production:
            settings_module = "smsmali.settings_production"
        else:
            settings_module = "smsmali.settings"
        
        self.run_command(
            f"{venv_python} {manage_py} migrate --settings={settings_module}",
            "Running database migrations"
        )

    def create_wsgi_file(self):
        """Create WSGI configuration file for PythonAnywhere"""
        wsgi_content = f'''# WSGI configuration for SMSMali on PythonAnywhere

import os
import sys

# Add your project directory to the sys.path
path = '/home/<USER>/{self.project_name}'
if path not in sys.path:
    sys.path.insert(0, path)

# Set the Django settings module
if os.environ.get('DJANGO_ENV') == 'production':
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smsmali.settings_production')
else:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smsmali.settings')

# Import Django WSGI application
from django.core.wsgi import get_wsgi_application
application = get_wsgi_application()
'''
        
        wsgi_file = self.pa_project_dir / "wsgi.py"
        with open(wsgi_file, 'w') as f:
            f.write(wsgi_content)
        
        print(f"   ✅ Created WSGI file: {wsgi_file}")

    def create_deployment_info(self):
        """Create deployment information file"""
        deployment_info = {
            "project_name": self.project_name,
            "domain": self.domain,
            "deployment_time": datetime.now().isoformat(),
            "environment": "production" if self.production else "development",
            "python_version": f"{sys.version_info.major}.{sys.version_info.minor}",
            "django_settings": "smsmali.settings_production" if self.production else "smsmali.settings"
        }
        
        info_file = self.base_dir / "deployment_info.json"
        with open(info_file, 'w') as f:
            json.dump(deployment_info, f, indent=2)
        
        print(f"   ✅ Created deployment info: {info_file}")

    def deploy(self):
        """Main deployment process"""
        print("🚀 Starting deployment process...")
        
        # Check prerequisites
        node_available = self.check_prerequisites()
        
        # Setup virtual environment
        self.setup_virtual_environment()
        
        # Build frontend if Node.js is available
        if node_available:
            self.build_frontend()
        
        # Setup Django for production
        if self.production:
            self.setup_django_settings()
        
        # Collect static files
        self.collect_static_files()
        
        # Run migrations
        self.run_migrations()
        
        # Create WSGI file
        self.create_wsgi_file()
        
        # Create deployment info
        self.create_deployment_info()
        
        print("\n🎉 Deployment completed successfully!")
        print(f"🌐 Your application should be available at: https://{self.domain}")
        print("\n📋 Next steps:")
        print("1. Go to PythonAnywhere Web tab")
        print("2. Create a new web app or reload existing one")
        print(f"3. Set WSGI file to: /home/<USER>/{self.project_name}/wsgi.py")
        print("4. Set static files mapping: /static/ -> /home/<USER>/smsmali/staticfiles")
        print("5. Set media files mapping: /media/ -> /home/<USER>/smsmali/media")
        
        if self.production:
            print("6. Set environment variable: DJANGO_ENV=production")
            print("7. Set environment variable: DJANGO_SECRET_KEY=your-secret-key")

def main():
    parser = argparse.ArgumentParser(description='Deploy SMSMali to PythonAnywhere')
    parser.add_argument('--production', action='store_true', 
                       help='Deploy in production mode')
    
    args = parser.parse_args()
    
    deployer = PythonAnywhereDeployer(production=args.production)
    deployer.deploy()

if __name__ == "__main__":
    main()
