#!/usr/bin/env python3
"""
Node.js Installation Checker
============================

This script checks if Node.js and npm are properly installed.

Usage:
    python check_nodejs.py

Author: SMSMali Development Team
"""

import subprocess
import sys
import os

def check_command(command, name):
    """Check if a command is available"""
    try:
        result = subprocess.run([command, '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ {name}: {version}")
            return True
        else:
            print(f"❌ {name}: Command failed")
            return False
    except FileNotFoundError:
        print(f"❌ {name}: Not found")
        return False
    except subprocess.TimeoutExpired:
        print(f"❌ {name}: Command timed out")
        return False
    except Exception as e:
        print(f"❌ {name}: Error - {e}")
        return False

def check_path():
    """Check if Node.js is in PATH"""
    path_dirs = os.environ.get('PATH', '').split(os.pathsep)
    node_dirs = [d for d in path_dirs if 'node' in d.lower()]
    
    if node_dirs:
        print(f"📁 Node.js directories in PATH:")
        for dir_path in node_dirs:
            print(f"   {dir_path}")
    else:
        print("⚠️  No Node.js directories found in PATH")

def show_installation_instructions():
    """Show installation instructions"""
    print("\n📋 INSTALLATION INSTRUCTIONS")
    print("=" * 30)
    print("1. 🌐 Download Node.js:")
    print("   • Go to: https://nodejs.org/")
    print("   • Download LTS version for Windows")
    print("   • Run the .msi installer")
    print("   • ✅ Check 'Add to PATH' during installation")
    print("\n2. 🔄 Restart your terminal/command prompt")
    print("\n3. ✅ Verify installation:")
    print("   node --version")
    print("   npm --version")
    print("\n4. 🏗️ Then run the build:")
    print("   python build_frontend.py --clean")

def main():
    print("🔍 Node.js Installation Checker")
    print("=" * 35)
    
    # Check Node.js
    node_ok = check_command('node', 'Node.js')
    
    # Check npm
    npm_ok = check_command('npm', 'npm')
    
    # Check PATH
    print("\n📁 PATH Information:")
    check_path()
    
    if node_ok and npm_ok:
        print("\n🎉 SUCCESS! Node.js and npm are properly installed.")
        print("You can now run: python build_frontend.py --clean")
    else:
        print("\n❌ Node.js and/or npm are not properly installed.")
        show_installation_instructions()

if __name__ == "__main__":
    main()
