import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import { FaCalculator, FaPhoneAlt } from 'react-icons/fa'

const CtaSection = () => {
  return (
    <section className="py-20 bg-primary text-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 right-0 w-96 h-96 bg-secondary opacity-10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-secondary opacity-10 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Need Quick Cash? <br />
              <span className="text-secondary">We've Got You Covered</span>
            </h2>
            <p className="text-lg mb-8 text-blue-100">
              Get instant cash loans on your valuables with the best rates in Midrand. 
              Our process is quick, confidential, and hassle-free.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link 
                to="/loan-calculator" 
                className="btn bg-white text-primary hover:bg-secondary hover:text-white border-none px-6 flex items-center gap-2"
              >
                <FaCalculator /> Calculate Your Loan
              </Link>
              <a 
                href="tel:+27123456789" 
                className="btn btn-outline border-white text-white hover:bg-white hover:text-primary px-6 flex items-center gap-2"
              >
                <FaPhoneAlt /> Call Us Now
              </a>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.6 }}
            className="bg-white p-8 rounded-lg shadow-lg"
          >
            <h3 className="text-2xl font-bold text-primary mb-6 text-center">
              Why Choose SMSMali?
            </h3>
            <ul className="space-y-4">
              <li className="flex items-start">
                <div className="bg-secondary text-white p-2 rounded-full mr-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <h4 className="font-semibold text-neutral">Best Rates in Town</h4>
                  <p className="text-neutral-content">We offer competitive interest rates and flexible repayment terms.</p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="bg-secondary text-white p-2 rounded-full mr-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <h4 className="font-semibold text-neutral">Quick & Confidential</h4>
                  <p className="text-neutral-content">Get cash in minutes with our discreet and private service.</p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="bg-secondary text-white p-2 rounded-full mr-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <h4 className="font-semibold text-neutral">Secure Storage</h4>
                  <p className="text-neutral-content">Your items are kept in a secure, insured facility until redemption.</p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="bg-secondary text-white p-2 rounded-full mr-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <h4 className="font-semibold text-neutral">Expert Appraisals</h4>
                  <p className="text-neutral-content">Our experienced team provides accurate valuations for your items.</p>
                </div>
              </li>
            </ul>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default CtaSection