import { Suspense, lazy } from 'react'
import { Helmet } from 'react-helmet'
import Hero from '../components/Hero'

// Lazy load components for better performance
const ServicesSection = lazy(() => import('../components/ServicesSection'))
const FeaturedProducts = lazy(() => import('../components/FeaturedProducts'))
const Testimonials = lazy(() => import('../components/Testimonials'))
const StatsSection = lazy(() => import('../components/StatsSection'))
const CtaSection = lazy(() => import('../components/CtaSection'))

// Loading fallback
const LoadingFallback = () => (
  <div className="flex justify-center items-center min-h-[400px]">
    <div className="loading loading-spinner loading-lg text-primary"></div>
  </div>
)

const HomePage = () => {
  return (
    <>
      <Helmet>
        <title>SMSMali - Premium Pawnshop in Midrand</title>
        <meta name="description" content="SMSMali is a trusted pawnshop based in Midrand offering loans, buying and selling of valuable items with the best rates and exceptional service." />
      </Helmet>

      {/* Hero Section */}
      <Hero />

      {/* Services Section */}
      <Suspense fallback={<LoadingFallback />}>
        <ServicesSection />
      </Suspense>

      {/* Featured Products Section */}
      <Suspense fallback={<LoadingFallback />}>
        <FeaturedProducts />
      </Suspense>

      {/* Testimonials Section */}
      <Suspense fallback={<LoadingFallback />}>
        <Testimonials />
      </Suspense>

      {/* Stats Section */}
      <Suspense fallback={<LoadingFallback />}>
        <StatsSection />
      </Suspense>

      {/* Call to Action Section */}
      <Suspense fallback={<LoadingFallback />}>
        <CtaSection />
      </Suspense>
    </>
  )
}

export default HomePage