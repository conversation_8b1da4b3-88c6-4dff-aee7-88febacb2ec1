# SMSMALI Pawnshop Application

A full-stack web application for a pawnshop business, featuring product listings, loan calculations, user authentication, and more.

## Project Structure

- **Frontend**: React application with Tailwind CSS and DaisyUI
- **Backend**: Django REST Framework API

## Features

### Products
- Product listings with categories, images, specifications
- Product search, filtering, and sorting
- Product reviews and ratings

### Loans
- Loan calculator for estimating loan amounts
- Loan application and management
- Loan terms and item types management

### Users
- User authentication with JWT
- User profiles with personal information
- Contact form and newsletter subscription

## Setup Instructions

### Backend Setup

1. Create a virtual environment:
   ```
   python -m venv django_venv
   ```

2. Activate the virtual environment:
   - Windows: `django_venv\Scripts\activate`
   - Unix/MacOS: `source django_venv/bin/activate`

3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

4. Run migrations:
   ```
   python manage.py makemigrations
   python manage.py migrate
   ```

5. Create a superuser:
   ```
   python manage.py createsuperuser
   ```

6. Start the development server:
   ```
   python manage.py runserver
   ```

### Frontend Setup

1. Navigate to the frontend directory:
   ```
   cd frontend
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Start the development server:
   ```
   npm run dev
   ```

## API Endpoints

### Products
- `GET /api/products/` - List all products
- `GET /api/products/{id}/` - Get product details
- `GET /api/products/categories/` - List all categories
- `POST /api/products/{id}/reviews/` - Submit a product review

### Loans
- `GET /api/loans/item-types/` - List all item types
- `GET /api/loans/loan-terms/` - List all loan terms
- `POST /api/loans/calculate/` - Calculate loan amount
- `GET /api/loans/` - List user's loans (authenticated)

### Users
- `POST /api/users/register/` - Register a new user
- `POST /api/users/token/` - Get JWT token
- `POST /api/users/token/refresh/` - Refresh JWT token
- `GET /api/users/profile/` - Get user profile (authenticated)
- `PUT /api/users/profile/` - Update user profile (authenticated)
- `POST /api/users/contact/` - Submit contact form
- `POST /api/users/newsletter/subscribe/` - Subscribe to newsletter

## Technologies Used

### Frontend
- React
- Tailwind CSS
- DaisyUI
- Framer Motion
- React Router
- React Helmet

### Backend
- Django
- Django REST Framework
- Simple JWT
- SQLite (development) / PostgreSQL (production)