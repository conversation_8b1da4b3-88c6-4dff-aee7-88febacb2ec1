import { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { FaFacebook, FaTwitter, FaInstagram, FaLinkedin, FaMapMarkerAlt, FaPhone, FaEnvelope, FaPaperPlane } from 'react-icons/fa'

const Footer = () => {
  const currentYear = new Date().getFullYear()
  const [email, setEmail] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [subscriptionStatus, setSubscriptionStatus] = useState(null)

  const handleSubscribe = async (e) => {
    e.preventDefault()
    if (!email) return
    
    setIsSubmitting(true)
    setSubscriptionStatus(null)
    
    try {
      const response = await fetch('/api/users/newsletter/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email })
      })
      
      if (response.ok) {
        setSubscriptionStatus('success')
        setEmail('')
        
        // Clear success message after 5 seconds
        setTimeout(() => {
          setSubscriptionStatus(null)
        }, 5000)
      } else {
        const errorData = await response.json()
        console.error('Newsletter subscription error:', errorData)
        setSubscriptionStatus('error')
        
        // Clear error message after 5 seconds
        setTimeout(() => {
          setSubscriptionStatus(null)
        }, 5000)
      }
    } catch (error) {
      console.error('Newsletter subscription error:', error)
      setSubscriptionStatus('error')
      
      // Clear error message after 5 seconds
      setTimeout(() => {
        setSubscriptionStatus(null)
      }, 5000)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <footer className="bg-neutral text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Company Info */}
          <div>
            <h3 className="text-2xl font-bold mb-4">SMS<span className="text-secondary">Mali</span></h3>
            <p className="mb-4 text-gray-300">
              Your trusted pawnshop in Midrand offering the best rates and exceptional service for loans and valuable items.
            </p>
            <div className="flex space-x-4">
              <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" className="hover:text-secondary transition-colors">
                <FaFacebook size={20} />
              </a>
              <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" className="hover:text-secondary transition-colors">
                <FaTwitter size={20} />
              </a>
              <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" className="hover:text-secondary transition-colors">
                <FaInstagram size={20} />
              </a>
              <a href="https://linkedin.com" target="_blank" rel="noopener noreferrer" className="hover:text-secondary transition-colors">
                <FaLinkedin size={20} />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-xl font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-gray-300 hover:text-secondary transition-colors">Home</Link>
              </li>
              <li>
                <Link to="/about" className="text-gray-300 hover:text-secondary transition-colors">About Us</Link>
              </li>
              <li>
                <Link to="/services" className="text-gray-300 hover:text-secondary transition-colors">Our Services</Link>
              </li>
              <li>
                <Link to="/products" className="text-gray-300 hover:text-secondary transition-colors">Products</Link>
              </li>
              <li>
                <Link to="/loan-calculator" className="text-gray-300 hover:text-secondary transition-colors">Loan Calculator</Link>
              </li>
              <li>
                <Link to="/contact" className="text-gray-300 hover:text-secondary transition-colors">Contact Us</Link>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-xl font-semibold mb-4">Our Services</h3>
            <ul className="space-y-2">
              <li className="text-gray-300 hover:text-secondary transition-colors">
                <Link to="/services#loans">Pawn Loans</Link>
              </li>
              <li className="text-gray-300 hover:text-secondary transition-colors">
                <Link to="/services#buying">Buying Valuables</Link>
              </li>
              <li className="text-gray-300 hover:text-secondary transition-colors">
                <Link to="/services#selling">Selling Items</Link>
              </li>
              <li className="text-gray-300 hover:text-secondary transition-colors">
                <Link to="/services#appraisal">Item Appraisal</Link>
              </li>
              <li className="text-gray-300 hover:text-secondary transition-colors">
                <Link to="/services#jewelry">Jewelry Cleaning</Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-xl font-semibold mb-4">Contact Us</h3>
            <ul className="space-y-4">
              <li className="flex items-start space-x-3">
                <FaMapMarkerAlt className="text-secondary mt-1" />
                <span className="text-gray-300">123 Main Street, Midrand, Gauteng, South Africa</span>
              </li>
              <li className="flex items-center space-x-3">
                <FaPhone className="text-secondary" />
                <a href="tel:+27123456789" className="text-gray-300 hover:text-secondary transition-colors">+27 12 345 6789</a>
              </li>
              <li className="flex items-center space-x-3">
                <FaEnvelope className="text-secondary" />
                <a href="mailto:<EMAIL>" className="text-gray-300 hover:text-secondary transition-colors"><EMAIL></a>
              </li>
            </ul>
          </div>
          
          {/* Newsletter */}
          <div>
            <h3 className="text-xl font-semibold mb-4">Newsletter</h3>
            <p className="mb-4 text-gray-300">Subscribe to our newsletter for the latest updates and offers.</p>
            
            {subscriptionStatus === 'success' && (
              <div className="alert alert-success bg-green-800 text-white text-sm p-2 rounded mb-4">
                <span>Successfully subscribed to our newsletter!</span>
              </div>
            )}
            
            {subscriptionStatus === 'error' && (
              <div className="alert alert-error bg-red-800 text-white text-sm p-2 rounded mb-4">
                <span>Error subscribing. Please try again later.</span>
              </div>
            )}
            
            <form onSubmit={handleSubscribe} className="flex">
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Your email address"
                className="px-3 py-2 bg-gray-700 text-white rounded-l-md focus:outline-none focus:ring-1 focus:ring-secondary w-full"
                required
              />
              <button 
                type="submit" 
                className="bg-secondary hover:bg-secondary-focus text-white px-3 py-2 rounded-r-md transition-colors flex items-center justify-center"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <span className="loading loading-spinner loading-xs"></span>
                ) : (
                  <FaPaperPlane />
                )}
              </button>
            </form>
            <p className="mt-2 text-xs text-gray-400">We respect your privacy and will never share your information.</p>
          </div>
        </div>

        <div className="border-t border-gray-700 mt-10 pt-6 text-center text-gray-400">
          <p>&copy; {currentYear} SMSMali. All rights reserved.</p>
        </div>
      </div>
    </footer>
  )
}

export default Footer