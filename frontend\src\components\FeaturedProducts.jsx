import { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { motion } from 'framer-motion'
import { FaStar, FaArrowRight } from 'react-icons/fa'

// Mock data for featured products
const mockProducts = [
  {
    id: 1,
    name: 'Gold Chain Necklace',
    category: 'Jewelry',
    price: 4999.99,
    rating: 4.8,
    image: 'https://images.unsplash.com/photo-1599643478518-a784e5dc4c8f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
  },
  {
    id: 2,
    name: 'Diamond Engagement Ring',
    category: 'Jewelry',
    price: 8999.99,
    rating: 5.0,
    image: 'https://images.unsplash.com/photo-1605100804763-247f67b3557e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
  },
  {
    id: 3,
    name: 'Apple MacBook Pro',
    category: 'Electronics',
    price: 12999.99,
    rating: 4.7,
    image: 'https://images.unsplash.com/photo-1517336714731-489689fd1ca8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
  },
  {
    id: 4,
    name: 'Rolex Submariner Watch',
    category: 'Watches',
    price: 59999.99,
    rating: 4.9,
    image: 'https://images.unsplash.com/photo-1523170335258-f5ed11844a49?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
  },
  {
    id: 5,
    name: 'Canon EOS R5 Camera',
    category: 'Electronics',
    price: 24999.99,
    rating: 4.6,
    image: 'https://images.unsplash.com/photo-1516035069371-29a1b244cc32?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
  },
  {
    id: 6,
    name: 'Diamond Tennis Bracelet',
    category: 'Jewelry',
    price: 7999.99,
    rating: 4.8,
    image: 'https://images.unsplash.com/photo-1611652022419-a9419f74343d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
  },
]

const FeaturedProducts = () => {
  const [products, setProducts] = useState([])
  
  useEffect(() => {
    // In a real application, you would fetch products from your API
    // For now, we'll use the mock data
    setProducts(mockProducts)
  }, [])

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }
  
  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  }

  // Format price with commas and 2 decimal places
  const formatPrice = (price) => {
    return price.toLocaleString('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 2
    })
  }

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Featured <span className="text-primary">Products</span>
          </h2>
          <p className="text-neutral max-w-2xl mx-auto">
            Discover our collection of high-quality pre-owned items at unbeatable prices. 
            All products are thoroughly inspected and come with a warranty.
          </p>
        </motion.div>

        <motion.div 
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {products.map((product) => (
            <motion.div 
              key={product.id}
              variants={itemVariants}
              className="card bg-base-100 shadow-lg overflow-hidden card-hover"
            >
              <figure className="relative h-64 overflow-hidden">
                <img 
                  src={product.image} 
                  alt={product.name} 
                  className="w-full h-full object-cover transition-transform duration-500 hover:scale-110" 
                />
                <div className="absolute top-4 left-4 bg-secondary text-white text-xs font-semibold px-2 py-1 rounded">
                  {product.category}
                </div>
              </figure>
              <div className="card-body">
                <h3 className="card-title text-lg font-bold">{product.name}</h3>
                <div className="flex items-center mt-1 mb-2">
                  <div className="flex items-center">
                    <FaStar className="text-yellow-500" />
                    <span className="ml-1 text-sm font-medium">{product.rating}</span>
                  </div>
                </div>
                <p className="text-xl font-bold text-primary">
                  {formatPrice(product.price)}
                </p>
                <div className="card-actions justify-end mt-4">
                  <Link 
                    to={`/products/${product.id}`} 
                    className="btn btn-primary btn-sm text-white"
                  >
                    View Details
                  </Link>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="text-center mt-12"
        >
          <Link 
            to="/products" 
            className="btn btn-outline btn-primary px-8 group flex items-center mx-auto gap-2"
          >
            View All Products
            <FaArrowRight className="group-hover:translate-x-1 transition-transform" />
          </Link>
        </motion.div>
      </div>
    </section>
  )
}

export default FeaturedProducts