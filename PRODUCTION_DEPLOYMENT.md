# Production Deployment Guide for SMSMali Pawnshop Application

This guide covers the complete process of deploying the SMSMali application to PythonAnywhere.

## Prerequisites

- PythonAnywhere account with web app capability
- Git repository with your code
- MySQL database configured on PythonAnywhere

## Phase 1: Local Preparation

### 1. Build Frontend for Production

```bash
# Run the production build script
python build_production.py
```

This will:
- Install frontend dependencies
- Build the React app for production
- Create necessary directories

### 2. Create Environment File

```bash
# Copy the example file
cp .env.example .env

# Edit with your actual values
nano .env
```

Fill in these values:
- `SECRET_KEY`: Generate a new Django secret key
- `DB_PASSWORD`: Your PythonAnywhere MySQL password
- `DB_NAME`: Usually `yourusername$databasename`
- `DB_USER`: Your PythonAnywhere username

### 3. Test Production Settings Locally (Optional)

```bash
# Run with production settings
python manage_production.py runserver
```

### 4. Commit and Push to Git

```bash
git add .
git commit -m "Production ready deployment"
git push origin main
```

## Phase 2: PythonAnywhere Setup

### 1. Clone Repository

```bash
# SSH into PythonAnywhere console
cd /home/<USER>
git clone https://github.com/yourusername/smsmali.git smsmali
cd smsmali
```

### 2. Run Deployment Script

```bash
# Make script executable
chmod +x deploy_pythonanywhere.py

# Run deployment setup
python3.10 deploy_pythonanywhere.py
```

### 3. Configure Environment Variables

```bash
# Create .env file
cp .env.example .env
nano .env
```

Update with your PythonAnywhere MySQL credentials:
```
SECRET_KEY=your-generated-secret-key
DEBUG=False
DB_NAME=smsmali$smsmali
DB_USER=smsmali
DB_PASSWORD=your-pythonanywhere-mysql-password
DB_HOST=smsmali.mysql.pythonanywhere-services.com
DB_PORT=3306
```

### 4. Complete Django Setup

```bash
# Activate virtual environment
source venv/bin/activate

# Run migrations
python manage_production.py migrate

# Collect static files
python manage_production.py collectstatic --noinput

# Create superuser
python manage_production.py createsuperuser
```

## Phase 3: Web App Configuration

### 1. Create Web App

1. Go to PythonAnywhere Dashboard
2. Click "Web" tab
3. Click "Add a new web app"
4. Choose "Manual configuration"
5. Select Python 3.10

### 2. Configure WSGI File

In the web app configuration:

**Source code**: `/home/<USER>/smsmali`
**Working directory**: `/home/<USER>/smsmali`

Edit WSGI file at `/var/www/smsmali_pythonanywhere_com_wsgi.py`:

```python
import os
import sys

# Add project to path
path = '/home/<USER>/smsmali'
if path not in sys.path:
    sys.path.insert(0, path)

# Load environment variables
from dotenv import load_dotenv
load_dotenv('/home/<USER>/smsmali/.env')

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smsmali.settings_production')

from django.core.wsgi import get_wsgi_application
application = get_wsgi_application()
```

### 3. Set Virtual Environment

**Virtualenv**: `/home/<USER>/smsmali/venv`

### 4. Configure Static Files

Add static files mapping:
- **URL**: `/static/`
- **Directory**: `/home/<USER>/smsmali/staticfiles/`

Add media files mapping:
- **URL**: `/media/`
- **Directory**: `/home/<USER>/smsmali/media/`

### 5. Set up Database

1. Go to "Databases" tab
2. Create MySQL database: `smsmali$smsmali`
3. Note down the connection details

## Phase 4: Testing and Verification

### 1. Test the Application

1. Visit your domain: `https://smsmali.pythonanywhere.com`
2. Test API endpoints: `https://smsmali.pythonanywhere.com/api/`
3. Check Django admin: `https://smsmali.pythonanywhere.com/admin/`

### 2. Verify Frontend

1. Check if React app loads correctly
2. Test navigation and features
3. Verify API integration

### 3. Check Logs

```bash
# Check Django logs
tail -f /home/<USER>/smsmali/logs/django.log

# Check error logs
tail -f /var/log/smsmali.pythonanywhere.com.error.log
```

## Phase 5: Maintenance

### Updating the Application

```bash
# Pull latest changes
git pull origin main

# Activate virtual environment
source venv/bin/activate

# Update dependencies if needed
pip install -r requirements_production.txt

# Run migrations
python manage_production.py migrate

# Collect static files
python manage_production.py collectstatic --noinput

# Restart web app (from PythonAnywhere dashboard)
```

### Monitoring

- Check error logs regularly
- Monitor database performance
- Set up automated backups for media files
- Monitor disk usage

## Troubleshooting

### Common Issues

1. **Import errors**: Check virtual environment activation
2. **Database connection**: Verify MySQL credentials
3. **Static files not loading**: Check static files mapping
4. **Frontend not working**: Verify build process completed

### Debug Commands

```bash
# Check Django configuration
python manage_production.py check --deploy

# Test database connection
python manage_production.py dbshell

# Check static files collection
python manage_production.py findstatic --verbosity=2 <filename>
```

## Security Considerations

1. Never commit `.env` file to version control
2. Use strong SECRET_KEY
3. Enable SSL/HTTPS (automatic on PythonAnywhere)
4. Regular security updates
5. Monitor access logs

## Performance Optimization

1. Use database indexing
2. Optimize static files with compression
3. Implement caching where appropriate
4. Monitor and optimize database queries

## Backup Strategy

1. Regular database backups
2. Media files backup
3. Code repository backup
4. Environment configuration backup

---

For support, check the application logs or contact the development team.