from django.contrib import admin
from .models import ItemType, LoanTerm, Loan, LoanExtension, Payment, LoanCalculation

class LoanExtensionInline(admin.TabularInline):
    model = LoanExtension
    extra = 0

class PaymentInline(admin.TabularInline):
    model = Payment
    extra = 0

@admin.register(ItemType)
class ItemTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'loan_to_value_ratio', 'created_at']
    search_fields = ['name', 'description']

@admin.register(LoanTerm)
class LoanTermAdmin(admin.ModelAdmin):
    list_display = ['days', 'interest_rate_percentage', 'description', 'is_active']
    list_filter = ['is_active']
    list_editable = ['is_active']

@admin.register(Loan)
class LoanAdmin(admin.ModelAdmin):
    list_display = ['id', 'user', 'item_type', 'item_description', 'loan_amount', 
                    'total_repayment', 'status', 'start_date', 'due_date']
    list_filter = ['status', 'item_type', 'start_date', 'due_date']
    search_fields = ['user__username', 'user__email', 'item_description']
    readonly_fields = ['created_at', 'updated_at']
    inlines = [LoanExtensionInline, PaymentInline]
    
    fieldsets = (
        ('Loan Information', {
            'fields': ('user', 'status', 'start_date', 'due_date')
        }),
        ('Item Details', {
            'fields': ('item_type', 'item_description', 'item_value')
        }),
        ('Financial Details', {
            'fields': ('loan_amount', 'loan_term', 'interest_amount', 'total_repayment')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )

@admin.register(LoanExtension)
class LoanExtensionAdmin(admin.ModelAdmin):
    list_display = ['loan', 'extension_date', 'previous_due_date', 'new_due_date', 'extension_fee']
    list_filter = ['extension_date']
    search_fields = ['loan__user__username', 'loan__user__email', 'notes']

@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ['loan', 'amount', 'payment_date', 'payment_type']
    list_filter = ['payment_date', 'payment_type']
    search_fields = ['loan__user__username', 'loan__user__email', 'notes']

@admin.register(LoanCalculation)
class LoanCalculationAdmin(admin.ModelAdmin):
    list_display = ['item_type', 'item_value', 'loan_term', 'loan_amount', 'total_repayment', 'created_at']
    list_filter = ['item_type', 'loan_term', 'created_at']
    readonly_fields = ['created_at', 'ip_address']