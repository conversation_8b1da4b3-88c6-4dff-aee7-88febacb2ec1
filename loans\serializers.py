from rest_framework import serializers
from .models import ItemType, LoanTerm, Loan, LoanExtension, Payment, LoanCalculation

class ItemTypeSerializer(serializers.ModelSerializer):
    loan_to_value_percentage = serializers.SerializerMethodField()
    
    class Meta:
        model = ItemType
        fields = ['id', 'name', 'description', 'loan_to_value_ratio', 'loan_to_value_percentage']
    
    def get_loan_to_value_percentage(self, obj):
        return int(obj.loan_to_value_ratio * 100)

class LoanTermSerializer(serializers.ModelSerializer):
    interest_rate_percentage = serializers.FloatField(read_only=True)
    
    class Meta:
        model = LoanTerm
        fields = ['id', 'days', 'interest_rate', 'interest_rate_percentage', 'description']

class LoanCalculationSerializer(serializers.ModelSerializer):
    item_type = serializers.PrimaryKeyRelatedField(queryset=ItemType.objects.all())
    loan_term = serializers.PrimaryKeyRelatedField(queryset=LoanTerm.objects.filter(is_active=True))
    
    class Meta:
        model = LoanCalculation
        fields = ['item_type', 'item_value', 'loan_term', 'loan_amount', 'interest_amount', 'total_repayment']
        read_only_fields = ['loan_amount', 'interest_amount', 'total_repayment']
    
    def create(self, validated_data):
        # Calculate loan amount based on item value and LTV ratio
        item_type = validated_data['item_type']
        item_value = validated_data['item_value']
        loan_term = validated_data['loan_term']
        
        loan_amount = item_value * item_type.loan_to_value_ratio
        interest_amount = loan_amount * loan_term.interest_rate
        total_repayment = loan_amount + interest_amount
        
        # Get client IP if available
        request = self.context.get('request')
        ip_address = None
        if request:
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                ip_address = x_forwarded_for.split(',')[0]
            else:
                ip_address = request.META.get('REMOTE_ADDR')
        
        # Create and return the calculation
        return LoanCalculation.objects.create(
            item_type=item_type,
            item_value=item_value,
            loan_term=loan_term,
            loan_amount=loan_amount,
            interest_amount=interest_amount,
            total_repayment=total_repayment,
            ip_address=ip_address
        )

class PaymentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Payment
        fields = ['id', 'amount', 'payment_date', 'payment_type', 'notes']

class LoanExtensionSerializer(serializers.ModelSerializer):
    class Meta:
        model = LoanExtension
        fields = ['id', 'extension_date', 'previous_due_date', 'new_due_date', 'extension_fee', 'notes']

class LoanSerializer(serializers.ModelSerializer):
    item_type = ItemTypeSerializer(read_only=True)
    loan_term = LoanTermSerializer(read_only=True)
    payments = PaymentSerializer(many=True, read_only=True)
    extensions = LoanExtensionSerializer(many=True, read_only=True)
    days_remaining = serializers.SerializerMethodField()
    
    class Meta:
        model = Loan
        fields = ['id', 'item_type', 'item_description', 'item_value', 'loan_amount', 
                  'loan_term', 'interest_amount', 'total_repayment', 'status', 
                  'start_date', 'due_date', 'days_remaining', 'payments', 'extensions', 
                  'created_at']
    
    def get_days_remaining(self, obj):
        if obj.due_date and obj.status == 'active':
            from django.utils import timezone
            today = timezone.now().date()
            delta = obj.due_date - today
            return max(0, delta.days)
        return 0