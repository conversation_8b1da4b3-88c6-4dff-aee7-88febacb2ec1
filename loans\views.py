from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from .models import ItemType, LoanTerm, Loan, LoanCalculation
from .serializers import (
    ItemTypeSerializer, 
    LoanTermSerializer, 
    LoanSerializer, 
    LoanCalculationSerializer
)

class ItemTypeViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = ItemType.objects.all()
    serializer_class = ItemTypeSerializer

class LoanTermViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = LoanTerm.objects.filter(is_active=True)
    serializer_class = LoanTermSerializer

class LoanViewSet(viewsets.ModelViewSet):
    serializer_class = LoanSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['status']
    
    def get_queryset(self):
        return Loan.objects.filter(user=self.request.user).order_by('-created_at')

class LoanCalculationViewSet(viewsets.GenericViewSet):
    serializer_class = LoanCalculationSerializer
    
    @action(detail=False, methods=['post'])
    def calculate(self, request):
        serializer = self.get_serializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            calculation = serializer.save()
            return Response({
                'loan_amount': calculation.loan_amount,
                'interest_amount': calculation.interest_amount,
                'total_repayment': calculation.total_repayment,
                'item_type': calculation.item_type.name,
                'item_value': calculation.item_value,
                'loan_term_days': calculation.loan_term.days,
                'interest_rate_percentage': calculation.loan_term.interest_rate_percentage
            })
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def item_types(self, request):
        item_types = ItemType.objects.all()
        serializer = ItemTypeSerializer(item_types, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def loan_terms(self, request):
        loan_terms = LoanTerm.objects.filter(is_active=True)
        serializer = LoanTermSerializer(loan_terms, many=True)
        return Response(serializer.data)