/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: "#9f7aea",
        secondary: "#f6ad55",
        accent: "#4fd1c5",
        neutral: "#3D4451",
        "base-100": "#FFFFFF",
      },
      fontFamily: {
        sans: ['Poppins', 'sans-serif'],
        heading: ['Montserrat', 'sans-serif'],
        serif: ['Merriweather', 'serif'],
      },
      animation: {
        'float': 'float 6s ease-in-out infinite',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-20px)' },
        }
      },
    },
  },
  plugins: [require("daisyui")],
  daisyui: {
    themes: [
      {
        smsmali: {
          "primary": "#9f7aea",          // Purple
          "primary-focus": "#805ad5",    // Darker purple
          "primary-content": "#ffffff",  // White text on primary
          
          "secondary": "#f6ad55",        // Orange
          "secondary-focus": "#ed8936",  // Darker orange
          "secondary-content": "#ffffff", // White text on secondary
          
          "accent": "#4fd1c5",           // Teal
          "accent-focus": "#38b2ac",     // Darker teal
          "accent-content": "#ffffff",   // White text on accent
          
          "neutral": "#3d4451",          // Dark gray
          "neutral-focus": "#2a2e37",    // Darker gray
          "neutral-content": "#ffffff",  // White text on neutral
          
          "base-100": "#ffffff",         // White background
          "base-200": "#f7fafc",         // Light gray background
          "base-300": "#e2e8f0",         // Lighter gray background
          "base-content": "#1a202c",     // Dark text on base
          
          "info": "#63b3ed",             // Blue
          "success": "#68d391",          // Green
          "warning": "#f6e05e",          // Yellow
          "error": "#fc8181",            // Red

          "--rounded-box": "1rem",       // Border radius for cards and larger elements
          "--rounded-btn": "0.5rem",     // Border radius for buttons
          "--rounded-badge": "1.9rem",   // Border radius for badges
          "--animation-btn": "0.25s",    // Button click animation duration
          "--animation-input": "0.2s",   // Input focus animation duration
          "--btn-focus-scale": "0.95",   // Button focus scale
          "--border-btn": "1px",         // Button border width
          "--tab-border": "1px",         // Tab border width
          "--tab-radius": "0.5rem",      // Tab border radius
        },
      },
    ],
    darkTheme: "dark",
    base: true,
    styled: true,
    utils: true,
    logs: false,
    rtl: false,
    prefix: "",
  },
}