# Environment configuration for production deployment
# Copy this file to .env and fill in the actual values

# Django Configuration
SECRET_KEY=your-super-secret-key-here
DEBUG=False

# Database Configuration (MySQL for PythonAnywhere)
DB_NAME=smsmali$smsmali
DB_USER=smsmali
DB_PASSWORD=your-database-password-here
DB_HOST=smsmali.mysql.pythonanywhere-services.com
DB_PORT=3306

# Domain Configuration
DOMAIN=smsmali.pythonanywhere.com

# Email Configuration (optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password

# Additional Configuration
ALLOWED_HOSTS=smsmali.pythonanywhere.com,www.smsmali.pythonanywhere.com,127.0.0.1,localhost