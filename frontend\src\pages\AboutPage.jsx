import { motion } from 'framer-motion'
import { Helmet } from 'react-helmet'
import { FaHandshake, FaHistory, FaUsers, FaAward } from 'react-icons/fa'

const AboutPage = () => {
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6 }
    }
  }

  const staggerContainer = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: 0.2
      }
    }
  }

  return (
    <>
      <Helmet>
        <title>About SMSMali - Our Story & Mission</title>
        <meta name="description" content="Learn about SMSMali's journey, our mission, values, and the team behind <PERSON>rand's premier pawnshop." />
      </Helmet>

      {/* Hero Section */}
      <section className="relative bg-gradient-to-b from-primary/10 to-base-100 py-20 md:py-28">
        <div className="container mx-auto px-4">
          <motion.div 
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="max-w-4xl mx-auto text-center"
          >
            <motion.h1 
              variants={fadeIn}
              className="text-4xl md:text-5xl font-bold mb-6 text-gradient-primary"
            >
              About SMSMali
            </motion.h1>
            <motion.p 
              variants={fadeIn}
              className="text-lg md:text-xl mb-8 text-base-content/80"
            >
              Your trusted partner in Midrand for all your pawnshop needs since 2015
            </motion.p>
          </motion.div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="py-16 bg-base-100">
        <div className="container mx-auto px-4">
          <motion.div 
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center"
          >
            <motion.div variants={fadeIn}>
              <h2 className="text-3xl font-bold mb-6">Our Story</h2>
              <p className="mb-4 text-base-content/80">
                SMSMali was founded in 2015 with a simple mission: to provide fair, transparent, and reliable pawnshop services to the Midrand community. What started as a small family business has grown into one of the most trusted names in the industry.
              </p>
              <p className="mb-4 text-base-content/80">
                Our founder, Samuel Mali, recognized a need for a pawnshop that treated customers with respect and offered fair valuations. Having worked in the jewelry and electronics industries for over 15 years, he combined his expertise with a passion for helping people access quick cash when they needed it most.
              </p>
              <p className="text-base-content/80">
                Today, SMSMali continues to uphold these founding principles while expanding our services to meet the evolving needs of our community.
              </p>
            </motion.div>
            <motion.div 
              variants={fadeIn}
              className="rounded-lg overflow-hidden shadow-xl"
            >
              <img 
                src="/images/about-store.jpg" 
                alt="SMSMali Store" 
                className="w-full h-auto object-cover"
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.src = 'https://placehold.co/600x400/primary/white?text=SMSMali+Store';
                }}
              />
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Our Values Section */}
      <section className="py-16 bg-base-200">
        <div className="container mx-auto px-4">
          <motion.div 
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center max-w-3xl mx-auto mb-12"
          >
            <motion.h2 
              variants={fadeIn}
              className="text-3xl font-bold mb-6"
            >
              Our Values
            </motion.h2>
            <motion.p 
              variants={fadeIn}
              className="text-base-content/80"
            >
              At SMSMali, our values guide everything we do. They're the foundation of our business and the reason our customers trust us.
            </motion.p>
          </motion.div>

          <motion.div 
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {[
              {
                icon: <FaHandshake className="text-4xl text-primary" />,
                title: "Integrity",
                description: "We operate with complete transparency and honesty in all our dealings."
              },
              {
                icon: <FaUsers className="text-4xl text-primary" />,
                title: "Customer First",
                description: "Your satisfaction and trust are our top priorities."
              },
              {
                icon: <FaAward className="text-4xl text-primary" />,
                title: "Expertise",
                description: "Our team of experts provides accurate valuations and professional service."
              },
              {
                icon: <FaHistory className="text-4xl text-primary" />,
                title: "Reliability",
                description: "We're here when you need us, with consistent service you can count on."
              }
            ].map((value, index) => (
              <motion.div 
                key={index}
                variants={fadeIn}
                className="bg-base-100 p-6 rounded-lg shadow-md text-center hover:shadow-xl transition-shadow duration-300"
              >
                <div className="mb-4 flex justify-center">
                  {value.icon}
                </div>
                <h3 className="text-xl font-bold mb-3">{value.title}</h3>
                <p className="text-base-content/80">{value.description}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16 bg-base-100">
        <div className="container mx-auto px-4">
          <motion.div 
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="text-center max-w-3xl mx-auto mb-12"
          >
            <motion.h2 
              variants={fadeIn}
              className="text-3xl font-bold mb-6"
            >
              Meet Our Team
            </motion.h2>
            <motion.p 
              variants={fadeIn}
              className="text-base-content/80"
            >
              Our experienced team is dedicated to providing you with exceptional service and expert guidance.
            </motion.p>
          </motion.div>

          <motion.div 
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {[
              {
                name: "Samuel Mali",
                position: "Founder & CEO",
                bio: "With over 20 years of experience in valuables and pawnbroking, Samuel founded SMSMali with a vision to create a trusted community resource.",
                image: "/images/team-samuel.jpg"
              },
              {
                name: "Thandi Nkosi",
                position: "Head Appraiser",
                bio: "Thandi is our jewelry and precious metals expert with certifications in gemology and 15 years of industry experience.",
                image: "/images/team-thandi.jpg"
              },
              {
                name: "David Mokoena",
                position: "Electronics Specialist",
                bio: "David's expertise in electronics and gadgets ensures you get the most accurate valuation for your tech items.",
                image: "/images/team-david.jpg"
              }
            ].map((member, index) => (
              <motion.div 
                key={index}
                variants={fadeIn}
                className="bg-base-200 rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-shadow duration-300"
              >
                <div className="h-64 overflow-hidden">
                  <img 
                    src={member.image} 
                    alt={member.name} 
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.target.onerror = null;
                      e.target.src = `https://placehold.co/400x400/primary/white?text=${member.name.replace(' ', '+')}`;
                    }}
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-1">{member.name}</h3>
                  <p className="text-primary font-medium mb-3">{member.position}</p>
                  <p className="text-base-content/80">{member.bio}</p>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary text-primary-content">
        <div className="container mx-auto px-4 text-center">
          <motion.div 
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
            className="max-w-3xl mx-auto"
          >
            <motion.h2 
              variants={fadeIn}
              className="text-3xl font-bold mb-6"
            >
              Ready to Experience the SMSMali Difference?
            </motion.h2>
            <motion.p 
              variants={fadeIn}
              className="text-lg mb-8"
            >
              Visit our store today or contact us to learn more about our services.
            </motion.p>
            <motion.div variants={fadeIn}>
              <a href="/contact" className="btn btn-secondary btn-lg mr-4">Contact Us</a>
              <a href="/services" className="btn btn-outline btn-lg border-white text-white hover:bg-white hover:text-primary">Our Services</a>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </>
  )
}

export default AboutPage