#!/usr/bin/env python3
"""
Simple Deployment Script for SMSMali on PythonAnywhere
======================================================

This script provides a simplified deployment process that handles
dependency issues and ensures latest package versions are installed.

Usage:
    python deploy_simple.py [--production]

Author: SMSMali Development Team
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import argparse

class SimpleDeployer:
    def __init__(self, production=False):
        self.production = production
        self.current_dir = Path.cwd()
        self.venv_dir = self.current_dir / "venv"
        
        print("🚀 SMSMali Simple Deployment")
        print(f"📁 Directory: {self.current_dir}")
        print(f"🌍 Mode: {'Production' if production else 'Development'}")
        print("-" * 50)

    def run_command(self, command, description, check=True):
        """Execute a command with error handling"""
        print(f"⚡ {description}...")
        try:
            if isinstance(command, str):
                result = subprocess.run(command, shell=True, check=check, 
                                      capture_output=True, text=True)
            else:
                result = subprocess.run(command, check=check, 
                                      capture_output=True, text=True)
            
            if result.stdout:
                # Show last few lines to avoid clutter
                lines = result.stdout.strip().split('\n')
                for line in lines[-3:]:
                    if line.strip():
                        print(f"   {line}")
            
            print(f"   ✅ {description} completed")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"   ❌ Error: {e}")
            if e.stderr:
                print(f"   📝 Details: {e.stderr}")
            if check:
                return False
            return False

    def check_python_version(self):
        """Check Python version"""
        version = sys.version_info
        print(f"🐍 Python version: {version.major}.{version.minor}.{version.micro}")
        
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            print("❌ Python 3.8+ required")
            return False
        
        print("✅ Python version compatible")
        return True

    def create_virtual_environment(self):
        """Create virtual environment"""
        if self.venv_dir.exists():
            print("♻️ Virtual environment exists, recreating...")
            shutil.rmtree(self.venv_dir)
        
        return self.run_command(
            f"{sys.executable} -m venv {self.venv_dir}",
            "Creating virtual environment"
        )

    def get_pip_command(self):
        """Get the correct pip command for the platform"""
        if os.name == 'nt':  # Windows
            return str(self.venv_dir / "Scripts" / "pip.exe")
        else:  # Unix/Linux
            return str(self.venv_dir / "bin" / "pip")

    def get_python_command(self):
        """Get the correct python command for the platform"""
        if os.name == 'nt':  # Windows
            return str(self.venv_dir / "Scripts" / "python.exe")
        else:  # Unix/Linux
            return str(self.venv_dir / "bin" / "python")

    def upgrade_pip(self):
        """Upgrade pip to latest version"""
        pip_cmd = self.get_pip_command()
        return self.run_command(
            f"{pip_cmd} install --upgrade pip",
            "Upgrading pip to latest version"
        )

    def install_core_dependencies(self):
        """Install core dependencies first"""
        pip_cmd = self.get_pip_command()
        
        # Install core packages first
        core_packages = [
            "Django>=4.2,<5.0",
            "djangorestframework>=3.14",
            "django-cors-headers>=4.3",
            "django-filter>=23.0",
            "Pillow>=10.0",
            "python-dotenv>=1.0",
            "whitenoise>=6.6"
        ]
        
        for package in core_packages:
            if not self.run_command(
                f"{pip_cmd} install '{package}'",
                f"Installing {package.split('>=')[0]}"
            ):
                print(f"⚠️ Failed to install {package}, continuing...")
        
        return True

    def install_requirements(self):
        """Install requirements from file"""
        pip_cmd = self.get_pip_command()
        requirements_file = self.current_dir / "requirements.txt"
        
        if requirements_file.exists():
            return self.run_command(
                f"{pip_cmd} install -r {requirements_file}",
                "Installing requirements from requirements.txt",
                check=False  # Don't fail if some packages can't install
            )
        else:
            print("⚠️ requirements.txt not found, using core dependencies only")
            return True

    def create_production_settings(self):
        """Create production settings"""
        if not self.production:
            return True
        
        settings_dir = self.current_dir / "smsmali"
        if not settings_dir.exists():
            print("❌ Django settings directory not found")
            return False
        
        settings_file = settings_dir / "settings_production.py"
        
        settings_content = f'''# Production settings for PythonAnywhere
import os
from .settings import *

# Security settings
DEBUG = False
ALLOWED_HOSTS = ['smsmali.pythonanywhere.com', 'www.smsmali.pythonanywhere.com', 'localhost', '127.0.0.1']

# Secret key from environment variable
SECRET_KEY = os.environ.get('DJANGO_SECRET_KEY', 'your-secret-key-here-change-in-production')

# Database configuration
DATABASES = {{
    'default': {{
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': '{self.current_dir}/db.sqlite3',
    }}
}}

# Static files configuration
STATIC_URL = '/static/'
STATIC_ROOT = '{self.current_dir}/staticfiles'
STATICFILES_DIRS = [
    '{self.current_dir}/static',
]

# Media files configuration
MEDIA_URL = '/media/'
MEDIA_ROOT = '{self.current_dir}/media'

# CORS settings for production
CORS_ALLOWED_ORIGINS = [
    "https://smsmali.pythonanywhere.com",
    "https://www.smsmali.pythonanywhere.com",
]

# Security settings
SECURE_SSL_REDIRECT = False  # PythonAnywhere handles SSL
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True

# Additional settings
USE_TZ = True
TIME_ZONE = 'UTC'

# Logging
LOGGING = {{
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {{
        'file': {{
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': '{self.current_dir}/django.log',
        }},
    }},
    'loggers': {{
        'django': {{
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        }},
    }},
}}
'''
        
        with open(settings_file, 'w') as f:
            f.write(settings_content)
        
        print(f"✅ Production settings created: {settings_file}")
        return True

    def deploy_frontend(self):
        """Deploy React frontend"""
        print("\n🎨 Deploying React frontend...")

        frontend_dir = self.current_dir / "frontend"
        if not frontend_dir.exists():
            print("⚠️ Frontend directory not found, skipping frontend deployment")
            return True

        # Run the frontend deployment script
        deploy_frontend_script = self.current_dir / "deploy_frontend.py"
        if deploy_frontend_script.exists():
            python_cmd = self.get_python_command()
            return self.run_command(
                f"{python_cmd} {deploy_frontend_script} --no-build",
                "Deploying React frontend"
            )
        else:
            print("⚠️ deploy_frontend.py not found, skipping frontend deployment")
            return True

    def run_django_commands(self):
        """Run Django management commands"""
        python_cmd = self.get_python_command()
        manage_py = self.current_dir / "manage.py"

        if not manage_py.exists():
            print("❌ manage.py not found")
            return False

        # Set Django settings module
        settings_module = "smsmali.settings_production" if self.production else "smsmali.settings"
        env = os.environ.copy()
        env['DJANGO_SETTINGS_MODULE'] = settings_module

        commands = [
            "collectstatic --noinput --clear",
            "migrate"
        ]

        for cmd in commands:
            full_cmd = f"{python_cmd} {manage_py} {cmd}"
            if not self.run_command(full_cmd, f"Running: python manage.py {cmd}"):
                print(f"⚠️ Command failed: {cmd}")

        return True

    def create_wsgi_file(self):
        """Create WSGI file"""
        wsgi_content = f'''# WSGI configuration for SMSMali

import os
import sys

# Add project directory to Python path
path = '{self.current_dir}'
if path not in sys.path:
    sys.path.insert(0, path)

# Set Django settings module
if os.environ.get('DJANGO_ENV') == 'production':
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smsmali.settings_production')
else:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smsmali.settings')

# Import Django WSGI application
from django.core.wsgi import get_wsgi_application
application = get_wsgi_application()
'''
        
        wsgi_file = self.current_dir / "wsgi.py"
        with open(wsgi_file, 'w') as f:
            f.write(wsgi_content)
        
        print(f"✅ WSGI file created: {wsgi_file}")
        return True

    def show_next_steps(self):
        """Show next steps"""
        print("\n🎉 Deployment completed!")
        print("\n📋 NEXT STEPS FOR PYTHONANYWHERE:")
        print("=" * 40)
        print("1. Go to PythonAnywhere Web tab")
        print("2. Create/configure web app:")
        print(f"   • Source code: {self.current_dir}")
        print(f"   • WSGI file: {self.current_dir}/wsgi.py")
        print(f"   • Virtual environment: {self.current_dir}/venv")
        print("3. Static files mapping:")
        print(f"   • URL: /static/ → Directory: {self.current_dir}/staticfiles")
        print(f"   • URL: /media/ → Directory: {self.current_dir}/media")
        print("4. Environment variables:")
        print("   • DJANGO_ENV = production")
        print("   • DJANGO_SECRET_KEY = your-secret-key")
        print("5. Reload web app")

    def deploy(self):
        """Main deployment process"""
        steps = [
            ("Checking Python version", self.check_python_version),
            ("Creating virtual environment", self.create_virtual_environment),
            ("Upgrading pip", self.upgrade_pip),
            ("Installing core dependencies", self.install_core_dependencies),
            ("Installing requirements", self.install_requirements),
            ("Creating production settings", self.create_production_settings),
            ("Deploying frontend", self.deploy_frontend),
            ("Running Django commands", self.run_django_commands),
            ("Creating WSGI file", self.create_wsgi_file),
        ]
        
        for description, step_func in steps:
            print(f"\n🔄 {description}...")
            if not step_func():
                print(f"❌ Failed: {description}")
                return False
        
        self.show_next_steps()
        return True

def main():
    parser = argparse.ArgumentParser(description='Simple SMSMali deployment')
    parser.add_argument('--production', action='store_true', 
                       help='Deploy in production mode')
    
    args = parser.parse_args()
    
    deployer = SimpleDeployer(production=args.production)
    success = deployer.deploy()
    
    if success:
        print("\n✅ Deployment successful!")
        return 0
    else:
        print("\n❌ Deployment failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
