import { useState, useEffect } from 'react'
import { use<PERSON><PERSON>ms, Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import { Helmet } from 'react-helmet'
import { FaStar, FaStarHalfAlt, FaRegStar, FaArrowLeft, FaShoppingCart, FaHeart, FaShare, FaCheck, FaInfoCircle } from 'react-icons/fa'

const ProductDetailPage = () => {
  const { id } = useParams()
  const [product, setProduct] = useState(null)
  const [loading, setLoading] = useState(true)
  const [mainImage, setMainImage] = useState('')
  const [quantity, setQuantity] = useState(1)
  const [tab, setTab] = useState('description')
  
  // Mock product data - in a real app, this would come from an API
  const mockProducts = [
    {
      id: "1",
      name: "Vintage Gold Necklace",
      category: "jewelry",
      subcategory: "necklaces",
      price: 4999.99,
      rating: 4.8,
      images: [
        "/images/products/gold-necklace.jpg",
        "/images/products/gold-necklace-2.jpg",
        "/images/products/gold-necklace-3.jpg",
      ],
      description: "Elegant 18K gold vintage necklace with intricate detailing. This beautiful piece features a delicate chain and a stunning pendant that catches the light perfectly. A timeless addition to any jewelry collection.",
      condition: "Excellent",
      featured: true,
      specifications: [
        { name: "Material", value: "18K Gold" },
        { name: "Weight", value: "8.5g" },
        { name: "Length", value: "18 inches" },
        { name: "Clasp Type", value: "Lobster Clasp" },
        { name: "Style", value: "Vintage" },
        { name: "Hallmark", value: "Yes" }
      ],
      relatedProducts: [2, 7, 9],
      reviews: [
        { id: 1, user: "Sarah M.", rating: 5, date: "2023-10-15", comment: "Absolutely beautiful necklace! The craftsmanship is exceptional and it looks even better in person." },
        { id: 2, user: "Michael T.", rating: 4, date: "2023-09-22", comment: "Bought this for my wife's birthday and she loves it. Great quality for the price." }
      ]
    },
    {
      id: "2",
      name: "Diamond Engagement Ring",
      category: "jewelry",
      subcategory: "rings",
      price: 12500.00,
      rating: 5.0,
      images: [
        "/images/products/diamond-ring.jpg",
        "/images/products/diamond-ring-2.jpg",
        "/images/products/diamond-ring-3.jpg",
      ],
      description: "Stunning 1.5 carat diamond ring set in white gold. This beautiful engagement ring features a brilliant-cut center diamond surrounded by smaller accent diamonds for maximum sparkle. The band is crafted from high-quality 14K white gold.",
      condition: "Like New",
      featured: true,
      specifications: [
        { name: "Metal", value: "14K White Gold" },
        { name: "Center Diamond", value: "1.5 carat" },
        { name: "Diamond Quality", value: "VS1 clarity, F color" },
        { name: "Accent Diamonds", value: "0.5 carat total weight" },
        { name: "Ring Size", value: "6 (can be resized)" },
        { name: "Certificate", value: "GIA Certified" }
      ],
      relatedProducts: [1, 7, 9],
      reviews: [
        { id: 1, user: "James L.", rating: 5, date: "2023-11-05", comment: "Proposed with this ring and she said yes! The diamond is absolutely stunning and sparkles beautifully." },
        { id: 2, user: "Rebecca W.", rating: 5, date: "2023-10-18", comment: "Incredible quality and value. I compared with retail stores and this was a much better deal." }
      ]
    },
    {
      id: "3",
      name: "Samsung Galaxy S21",
      category: "electronics",
      subcategory: "smartphones",
      price: 6999.99,
      rating: 4.5,
      images: [
        "/images/products/samsung-s21.jpg",
        "/images/products/samsung-s21-2.jpg",
        "/images/products/samsung-s21-3.jpg",
      ],
      description: "Barely used Samsung Galaxy S21 with 128GB storage. This premium smartphone features a stunning display, powerful processor, and excellent camera system. Perfect for photography enthusiasts and mobile gamers alike.",
      condition: "Very Good",
      featured: true,
      specifications: [
        { name: "Model", value: "Galaxy S21 5G" },
        { name: "Storage", value: "128GB" },
        { name: "RAM", value: "8GB" },
        { name: "Display", value: "6.2-inch Dynamic AMOLED 2X" },
        { name: "Processor", value: "Exynos 2100" },
        { name: "Camera", value: "12MP + 12MP + 64MP rear, 10MP front" },
        { name: "Battery", value: "4000mAh" },
        { name: "OS", value: "Android 11 (upgradable)" },
        { name: "Connectivity", value: "5G, Wi-Fi 6, Bluetooth 5.0" }
      ],
      relatedProducts: [4, 6, 8],
      reviews: [
        { id: 1, user: "David K.", rating: 5, date: "2023-10-30", comment: "Phone is in perfect condition, looks almost new. Great deal!" },
        { id: 2, user: "Priya S.", rating: 4, date: "2023-10-05", comment: "Very happy with this purchase. Battery life is still excellent and the camera takes amazing photos." }
      ]
    },
    {
      id: "4",
      name: "MacBook Pro 2021",
      category: "electronics",
      subcategory: "laptops",
      price: 15999.99,
      rating: 4.7,
      images: [
        "/images/products/macbook-pro.jpg",
        "/images/products/macbook-pro-2.jpg",
        "/images/products/macbook-pro-3.jpg",
      ],
      description: "Apple MacBook Pro with M1 chip, 16GB RAM, and 512GB SSD. This powerful laptop offers exceptional performance and battery life in a sleek, portable design. Perfect for professionals, creatives, and students alike.",
      condition: "Excellent",
      featured: true,
      specifications: [
        { name: "Model", value: "MacBook Pro 14-inch (2021)" },
        { name: "Processor", value: "Apple M1 Pro" },
        { name: "RAM", value: "16GB unified memory" },
        { name: "Storage", value: "512GB SSD" },
        { name: "Display", value: "14.2-inch Liquid Retina XDR" },
        { name: "Graphics", value: "16-core GPU" },
        { name: "Battery", value: "Up to 17 hours" },
        { name: "Ports", value: "3x Thunderbolt 4, HDMI, SD card slot, MagSafe" },
        { name: "OS", value: "macOS Monterey (upgradable)" }
      ],
      relatedProducts: [3, 6, 8],
      reviews: [
        { id: 1, user: "Alex T.", rating: 5, date: "2023-11-10", comment: "This MacBook is a beast! The M1 chip is incredibly fast and the battery lasts all day." },
        { id: 2, user: "Jessica M.", rating: 4, date: "2023-10-25", comment: "Great condition and performs like new. The screen is absolutely gorgeous." }
      ]
    },
    {
      id: "5",
      name: "Rolex Submariner",
      category: "watches",
      subcategory: "luxury",
      price: 89999.99,
      rating: 4.9,
      images: [
        "/images/products/rolex-submariner.jpg",
        "/images/products/rolex-submariner-2.jpg",
        "/images/products/rolex-submariner-3.jpg",
      ],
      description: "Authentic Rolex Submariner with black dial and stainless steel bracelet. This iconic luxury timepiece features a unidirectional rotatable bezel and is water-resistant to 300 meters. A perfect blend of style and functionality.",
      condition: "Excellent",
      featured: true,
      specifications: [
        { name: "Brand", value: "Rolex" },
        { name: "Model", value: "Submariner Date" },
        { name: "Reference Number", value: "126610LN" },
        { name: "Movement", value: "Automatic, Caliber 3235" },
        { name: "Case Material", value: "Oystersteel (904L stainless steel)" },
        { name: "Case Diameter", value: "41mm" },
        { name: "Bracelet", value: "Oyster, folding clasp" },
        { name: "Water Resistance", value: "300 meters / 1000 feet" },
        { name: "Bezel", value: "Unidirectional rotatable with Cerachrom insert" },
        { name: "Dial", value: "Black" },
        { name: "Crystal", value: "Scratch-resistant sapphire" },
        { name: "Year", value: "2021" }
      ],
      relatedProducts: [10, 13, 16],
      reviews: [
        { id: 1, user: "Robert J.", rating: 5, date: "2023-11-15", comment: "Absolutely stunning watch. The craftsmanship is impeccable and it keeps perfect time." },
        { id: 2, user: "Thomas B.", rating: 5, date: "2023-10-20", comment: "A true investment piece. The condition is excellent and it came with all original documentation." }
      ]
    }
  ]

  // Fetch product data
  useEffect(() => {
    // Simulate API call
    setLoading(true)
    setTimeout(() => {
      const foundProduct = mockProducts.find(p => p.id === id)
      if (foundProduct) {
        setProduct(foundProduct)
        setMainImage(foundProduct.images[0])
      }
      setLoading(false)
    }, 500)
  }, [id])

  // Handle quantity change
  const handleQuantityChange = (value) => {
    const newQuantity = Math.max(1, Math.min(10, quantity + value))
    setQuantity(newQuantity)
  }

  // Handle image change
  const handleImageChange = (image) => {
    setMainImage(image)
  }

  // Render star ratings
  const renderRating = (rating) => {
    const stars = []
    const fullStars = Math.floor(rating)
    const hasHalfStar = rating % 1 >= 0.5
    
    for (let i = 1; i <= 5; i++) {
      if (i <= fullStars) {
        stars.push(<FaStar key={i} className="text-yellow-500" />)
      } else if (i === fullStars + 1 && hasHalfStar) {
        stars.push(<FaStarHalfAlt key={i} className="text-yellow-500" />)
      } else {
        stars.push(<FaRegStar key={i} className="text-yellow-500" />)
      }
    }
    
    return (
      <div className="flex items-center">
        {stars}
        <span className="ml-1 text-sm text-gray-600">({rating})</span>
      </div>
    )
  }

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6 }
    }
  }

  const staggerContainer = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: 0.2
      }
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-16 flex justify-center items-center min-h-[60vh]">
        <div className="loading loading-spinner loading-lg text-primary"></div>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="container mx-auto px-4 py-16 text-center min-h-[60vh]">
        <h2 className="text-2xl font-bold mb-4">Product Not Found</h2>
        <p className="mb-8">The product you're looking for doesn't exist or has been removed.</p>
        <Link to="/products" className="btn btn-primary">
          <FaArrowLeft className="mr-2" /> Back to Products
        </Link>
      </div>
    )
  }

  return (
    <>
      <Helmet>
        <title>{product.name} - SMSMali Pawnshop</title>
        <meta name="description" content={`${product.description.substring(0, 150)}...`} />
      </Helmet>

      <section className="py-16 bg-base-100">
        <div className="container mx-auto px-4">
          {/* Breadcrumbs */}
          <div className="text-sm breadcrumbs mb-8">
            <ul>
              <li><Link to="/">Home</Link></li>
              <li><Link to="/products">Products</Link></li>
              <li><Link to={`/products?category=${product.category}`}>{product.category.charAt(0).toUpperCase() + product.category.slice(1)}</Link></li>
              <li className="text-primary">{product.name}</li>
            </ul>
          </div>

          <motion.div 
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="grid grid-cols-1 lg:grid-cols-2 gap-12"
          >
            {/* Product Images */}
            <motion.div variants={fadeIn} className="space-y-4">
              <div className="bg-base-200 rounded-lg overflow-hidden h-96 flex items-center justify-center">
                <img 
                  src={mainImage} 
                  alt={product.name}
                  className="max-w-full max-h-full object-contain"
                  onError={(e) => {
                    e.target.onerror = null;
                    e.target.src = `https://placehold.co/600x400/primary/white?text=${product.name.replace(/ /g, '+')}`;
                  }}
                />
              </div>
              
              <div className="flex space-x-4 overflow-x-auto pb-2">
                {product.images.map((image, index) => (
                  <div 
                    key={index}
                    className={`w-24 h-24 rounded-md overflow-hidden cursor-pointer border-2 ${mainImage === image ? 'border-primary' : 'border-transparent'}`}
                    onClick={() => handleImageChange(image)}
                  >
                    <img 
                      src={image} 
                      alt={`${product.name} - View ${index + 1}`}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.src = `https://placehold.co/200x200/primary/white?text=${index + 1}`;
                      }}
                    />
                  </div>
                ))}
              </div>
            </motion.div>
            
            {/* Product Details */}
            <motion.div variants={fadeIn}>
              <div className="mb-2">
                <span className="badge badge-primary">{product.condition}</span>
                {product.featured && <span className="badge badge-secondary ml-2">Featured</span>}
              </div>
              
              <h1 className="text-3xl font-bold mb-2">{product.name}</h1>
              
              <div className="mb-4">
                {renderRating(product.rating)}
              </div>
              
              <div className="text-3xl font-bold text-primary mb-6">
                R{product.price.toLocaleString()}
              </div>
              
              <div className="prose mb-6">
                <p>{product.description}</p>
              </div>
              
              <div className="divider"></div>
              
              {/* Add to Cart Section */}
              <div className="flex items-center mb-6">
                <div className="join mr-4">
                  <button 
                    className="join-item btn btn-sm"
                    onClick={() => handleQuantityChange(-1)}
                    disabled={quantity <= 1}
                  >
                    -
                  </button>
                  <div className="join-item btn btn-sm pointer-events-none">{quantity}</div>
                  <button 
                    className="join-item btn btn-sm"
                    onClick={() => handleQuantityChange(1)}
                    disabled={quantity >= 10}
                  >
                    +
                  </button>
                </div>
                
                <button className="btn btn-primary flex-grow">
                  <FaShoppingCart className="mr-2" /> Add to Cart
                </button>
              </div>
              
              <div className="flex space-x-4 mb-6">
                <button className="btn btn-outline btn-sm flex-1">
                  <FaHeart className="mr-2" /> Add to Wishlist
                </button>
                <button className="btn btn-outline btn-sm flex-1">
                  <FaShare className="mr-2" /> Share
                </button>
              </div>
              
              <div className="bg-base-200 p-4 rounded-lg mb-6">
                <div className="flex items-start">
                  <FaInfoCircle className="text-primary mt-1 mr-3" />
                  <div>
                    <p className="font-medium">Secure Transaction</p>
                    <p className="text-sm text-base-content/70">This item is available for immediate purchase. Contact us to arrange viewing or pickup.</p>
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center">
                  <FaCheck className="text-green-500 mr-2" />
                  <span>Quality checked by our experts</span>
                </div>
                <div className="flex items-center">
                  <FaCheck className="text-green-500 mr-2" />
                  <span>30-day warranty included</span>
                </div>
                <div className="flex items-center">
                  <FaCheck className="text-green-500 mr-2" />
                  <span>Secure payment options</span>
                </div>
              </div>
            </motion.div>
          </motion.div>
          
          {/* Product Tabs */}
          <div className="mt-16">
            <div className="tabs tabs-boxed mb-6">
              <button 
                className={`tab ${tab === 'description' ? 'tab-active' : ''}`}
                onClick={() => setTab('description')}
              >
                Description
              </button>
              <button 
                className={`tab ${tab === 'specifications' ? 'tab-active' : ''}`}
                onClick={() => setTab('specifications')}
              >
                Specifications
              </button>
              <button 
                className={`tab ${tab === 'reviews' ? 'tab-active' : ''}`}
                onClick={() => setTab('reviews')}
              >
                Reviews
              </button>
            </div>
            
            <div className="bg-base-200 p-6 rounded-lg">
              {tab === 'description' && (
                <div className="prose max-w-none">
                  <p>{product.description}</p>
                  <p>At SMSMali, we carefully inspect and authenticate all our items to ensure they meet our quality standards. This {product.name} has been thoroughly examined by our experts and is in {product.condition.toLowerCase()} condition.</p>
                  <p>We offer a 30-day warranty on this item, giving you peace of mind with your purchase. If you have any questions or would like to see this item in person, please don't hesitate to contact us or visit our store in Midrand.</p>
                </div>
              )}
              
              {tab === 'specifications' && (
                <div className="overflow-x-auto">
                  <table className="table">
                    <tbody>
                      {product.specifications.map((spec, index) => (
                        <tr key={index}>
                          <td className="font-medium">{spec.name}</td>
                          <td>{spec.value}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
              
              {tab === 'reviews' && (
                <div>
                  {product.reviews.length > 0 ? (
                    <div className="space-y-6">
                      {product.reviews.map(review => (
                        <div key={review.id} className="border-b border-base-300 pb-6 last:border-0 last:pb-0">
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <p className="font-bold">{review.user}</p>
                              <div className="flex items-center">
                                {[...Array(5)].map((_, i) => (
                                  <FaStar 
                                    key={i} 
                                    className={i < review.rating ? 'text-yellow-500' : 'text-gray-300'} 
                                  />
                                ))}
                              </div>
                            </div>
                            <span className="text-sm text-base-content/70">{review.date}</span>
                          </div>
                          <p>{review.comment}</p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p>No reviews yet. Be the first to review this product!</p>
                  )}
                  
                  <div className="mt-8">
                    <button className="btn btn-primary">
                      Write a Review
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
          
          {/* Related Products */}
          <div className="mt-16">
            <h2 className="text-2xl font-bold mb-6">You May Also Like</h2>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {mockProducts
                .filter(p => product.relatedProducts.includes(parseInt(p.id)))
                .map(relatedProduct => (
                  <div 
                    key={relatedProduct.id}
                    className="bg-base-200 rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-shadow duration-300"
                  >
                    <div className="h-48 overflow-hidden">
                      <img 
                        src={relatedProduct.images[0]} 
                        alt={relatedProduct.name}
                        className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                        onError={(e) => {
                          e.target.onerror = null;
                          e.target.src = `https://placehold.co/400x300/primary/white?text=${relatedProduct.name.replace(/ /g, '+')}`;
                        }}
                      />
                    </div>
                    
                    <div className="p-4">
                      <h3 className="font-bold mb-1 line-clamp-1">{relatedProduct.name}</h3>
                      
                      <div className="mb-2">
                        {renderRating(relatedProduct.rating)}
                      </div>
                      
                      <div className="flex justify-between items-center mb-3">
                        <span className="text-primary font-bold">R{relatedProduct.price.toLocaleString()}</span>
                        <span className="text-xs bg-base-300 px-2 py-1 rounded">{relatedProduct.condition}</span>
                      </div>
                      
                      <Link 
                        to={`/products/${relatedProduct.id}`}
                        className="btn btn-primary btn-sm w-full"
                      >
                        View Details
                      </Link>
                    </div>
                  </div>
                ))}
            </div>
          </div>
          
          {/* Back to Products */}
          <div className="mt-16 text-center">
            <Link to="/products" className="btn btn-outline">
              <FaArrowLeft className="mr-2" /> Back to All Products
            </Link>
          </div>
        </div>
      </section>
    </>
  )
}

export default ProductDetailPage